# MindSpace - Professional Meditation Website

A beautiful, professional meditation and wellness website inspired by leading companies like Headspace and Calm. Built with HTML, Tailwind CSS, and smooth animations.

## Features

- 🧘‍♀️ **Professional Design** - Clean, modern interface inspired by top meditation apps
- 🎵 **Audio Player** - Custom meditation audio player with controls
- 🎮 **Mindful Games** - 6 peaceful, mind-motivating games with watery animations
- 📱 **Responsive** - Works perfectly on all devices
- ✨ **Smooth Animations** - Floating elements, fade-ins, and hover effects
- 🎨 **Beautiful Gradients** - Eye-catching color schemes
- 🔊 **Sound Categories** - Sleep, Stress Relief, Focus meditations
- 🚀 **Fast Loading** - Optimized for performance

## Meditation Categories

1. **Sleep** - Bedtime stories and calming soundscapes
2. **Stress Relief** - Anxiety reduction and relaxation techniques
3. **Focus** - Concentration and mental clarity sessions

## 🎮 Mindful Games

Experience 4 peaceful, mind-motivating games designed to enhance your mental wellness:

1. **🫁 Breathing Harmony** - Interactive breathing exercises with expanding circles and guided rhythms (counts complete breath cycles)
2. **🏯 Zen Garden** - Create beautiful sand patterns with rake, stones, waves, and spiral tools (with satisfying audio feedback)
3. **🧠 Mindful Memory** - Gentle memory matching game with peaceful symbols and 3D card flip animations
4. **🌱 Gratitude Garden** - Plant virtual seeds of gratitude and watch your garden bloom

### ✨ Game Features:
- **Watery Animations** - Smooth, flowing transitions and ripple effects
- **Same-Page Loading** - All games load within the main page (no new tabs)
- **Touch Support** - Works on mobile devices and tablets
- **Peaceful Design** - Calming colors and gentle interactions
- **Progress Tracking** - Session timers, scores, and achievements
- **Keyboard Controls** - Accessible navigation and shortcuts

## Local Development

1. Clone or download the project
2. Open `index.html` in your browser
3. Or run a local server:
   ```bash
   python3 -m http.server 3000
   ```

## ✅ MP3 Meditation Sounds - Ready!

The website includes a **fully functional MP3 audio player** with **4 working meditation tracks** and **meditation timer**:

```
sounds/
├── sleep/
│   ├── ocean-waves.mp3          ✅ Working!
│   └── forest-rain.mp3          ✅ Working!
├── stress-relief/
│   └── breathing-exercise.mp3   ✅ Working!
└── focus/
    └── focus-meditation.mp3     ✅ Working!
```

### 🕐 **Meditation Timer Features:**
- **Time Options**: 2 min, 5 min, 10 min, 15 min, 30 min, or ∞ (infinite)
- **Auto-Loop**: Sounds automatically loop for the selected duration
- **Session Progress**: Visual progress bar showing meditation session completion
- **Auto-Stop**: Meditation automatically stops when timer reaches zero
- **Completion Notification**: Beautiful notification when session ends

### 🎵 Audio Player Features:
- **Play/Pause** controls
- **Progress bar** with click-to-seek
- **Volume control** with mute button
- **Previous/Next** track navigation
- **Track selection** from playlist
- **Real-time** duration display

### 📁 MP3 File Requirements:
- **Format**: MP3 (best web compatibility)
- **Quality**: 128-320 kbps recommended
- **Length**: 5-60 minutes ideal
- **Volume**: Normalize levels for consistency

## Deployment on Render

1. Connect your GitHub repository to Render
2. Choose "Static Site" as the service type
3. Set build command: `npm run build`
4. Set publish directory: `.` (root directory)
5. Deploy!

## Customization

- **Colors**: Modify the gradient classes in the `<style>` section
- **Content**: Update text, testimonials, and features in the HTML
- **Animations**: Adjust CSS animations and transitions
- **Audio Player**: Enhance with real audio functionality

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge

## License

MIT License - feel free to use for personal or commercial projects.

---

Made with ❤️ for your wellbeing and mental health journey.
