<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindSpace - Professional Meditation & Wellness</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧘</text></svg>">
    <meta name="description" content="Transform your mind with professional meditation sessions. Reduce stress, improve sleep, and find inner peace with MindSpace.">
    <meta name="keywords" content="meditation, mindfulness, stress relief, sleep, wellness, mental health">
    <meta property="og:title" content="MindSpace - Professional Meditation & Wellness">
    <meta property="og:description" content="Transform your mind with professional meditation sessions. Reduce stress, improve sleep, and find inner peace.">
    <meta property="og:type" content="website">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .font-display {
            font-family: 'Playfair Display', serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .meditation-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }
        .floating {
            animation: floating 6s ease-in-out infinite;
        }
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .fade-in {
            animation: fadeIn 1s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .pulse-slow {
            animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .audio-player {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        /* Custom Volume Slider */
        .slider {
            -webkit-appearance: none;
            background: linear-gradient(to right, #6366f1 0%, #6366f1 70%, #e5e7eb 70%, #e5e7eb 100%);
            outline: none;
            border-radius: 15px;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #6366f1;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #6366f1;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        /* Track Selection Active State */
        .track-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
        }

        .track-item.active .text-gray-900 {
            color: white !important;
        }

        .track-item.active .text-gray-500 {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        /* Breathing Animation for Play Button */
        .breathing {
            animation: breathe 4s ease-in-out infinite;
        }

        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Lesson card animations */
        .lesson-card {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .lesson-card:hover {
            transform: translateY(-8px) scale(1.02);
        }

        .lesson-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
            z-index: 1;
        }

        .lesson-card:hover::before {
            left: 100%;
        }

        .lesson-card > * {
            position: relative;
            z-index: 2;
        }

        .lesson-card:nth-child(1) { animation-delay: 0.1s; }
        .lesson-card:nth-child(2) { animation-delay: 0.2s; }
        .lesson-card:nth-child(3) { animation-delay: 0.3s; }
        .lesson-card:nth-child(4) { animation-delay: 0.4s; }
        .lesson-card:nth-child(5) { animation-delay: 0.5s; }
        .lesson-card:nth-child(6) { animation-delay: 0.6s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Category card hover effects */
        .category-card:hover {
            transform: translateY(-8px) scale(1.02);
        }

        /* Beautiful shadows */
        .shadow-3xl {
            box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
        }

        /* Subtle glow effect */
        .glow {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-white/90 backdrop-blur-md border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl sm:text-2xl font-bold gradient-bg bg-clip-text text-transparent">MindSpace</h1>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="#meditations" class="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">Meditations</a>
                        <a href="games.html" class="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">Games</a>
                        <a href="#features" class="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">Features</a>
                        <a href="#about" class="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">About</a>
                        <span id="userWelcome" class="text-gray-600 text-sm mr-4">Welcome!</span>
                        <button id="chatbotBtn" onclick="document.getElementById('chatbotModal').classList.remove('hidden')" class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-2">
                            <i class="fas fa-robot"></i>
                            <span>AI Chatbot</span>
                        </button>
                        <button onclick="logout()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ml-2">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </button>
                    </div>
                </div>
                <div class="md:hidden flex items-center space-x-2">
                    <button id="mobileChatbotBtn" onclick="document.getElementById('chatbotModal').classList.remove('hidden')" class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-1">
                        <i class="fas fa-robot text-sm"></i>
                        <span>AI</span>
                    </button>
                    <button id="mobileMenuBtn" class="text-gray-600 hover:text-gray-900 p-2">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            <!-- Mobile Menu -->
            <div id="mobileMenu" class="md:hidden hidden bg-white border-t border-gray-200">
                <div class="px-2 pt-2 pb-3 space-y-1">
                    <div id="mobileUserWelcome" class="px-3 py-2 text-sm text-gray-500 border-b border-gray-200 mb-2">Welcome!</div>
                    <a href="#meditations" class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-indigo-600 hover:bg-gray-50 rounded-md transition-colors">Meditations</a>
                    <a href="games.html" class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-indigo-600 hover:bg-gray-50 rounded-md transition-colors">Games</a>
                    <a href="#features" class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-indigo-600 hover:bg-gray-50 rounded-md transition-colors">Features</a>
                    <a href="#about" class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-indigo-600 hover:bg-gray-50 rounded-md transition-colors">About</a>
                    <button onclick="logout()" class="w-full text-left px-3 py-2 text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors flex items-center space-x-2">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="pt-16 min-h-screen flex items-center meditation-gradient">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20">
            <div class="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                <div class="text-white fade-in text-center lg:text-left">
                    <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-display font-bold mb-4 sm:mb-6 leading-tight">
                        Find Your Inner
                        <span class="block text-yellow-300">Peace</span>
                    </h1>
                    <p class="text-lg sm:text-xl mb-6 sm:mb-8 text-gray-100 leading-relaxed max-w-2xl mx-auto lg:mx-0">
                        Discover the tranquility within through mindful meditation. Let go of stress, embrace stillness, and cultivate a deeper connection with your true self.
                    </p>
                    <p class="text-base sm:text-lg mb-6 sm:mb-8 text-gray-200 leading-relaxed max-w-2xl mx-auto lg:mx-0 hidden sm:block">
                        In the quiet moments of meditation, we find not just peace, but the wisdom to navigate life with grace and compassion.
                    </p>
                    <div class="text-center lg:text-left">
                        <div class="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-white/20 backdrop-blur-sm rounded-full text-white/90 text-sm">
                            <i class="fas fa-heart mr-2 text-yellow-300"></i>
                            Begin your journey to inner peace
                        </div>
                    </div>
                </div>
                <div class="relative mt-8 lg:mt-0">
                    <div class="floating">
                        <div class="w-64 h-64 sm:w-80 sm:h-80 mx-auto glass-effect rounded-full flex items-center justify-center">
                            <div class="w-48 h-48 sm:w-60 sm:h-60 bg-white/30 rounded-full flex items-center justify-center">
                                <div class="w-32 h-32 sm:w-40 sm:h-40 bg-white/40 rounded-full flex items-center justify-center">
                                    <i class="fas fa-play text-white text-2xl sm:text-4xl ml-1 sm:ml-2"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute -top-6 -right-6 sm:-top-10 sm:-right-10 w-16 h-16 sm:w-20 sm:h-20 bg-yellow-300 rounded-full pulse-slow opacity-70"></div>
                    <div class="absolute -bottom-6 -left-6 sm:-bottom-10 sm:-left-10 w-12 h-12 sm:w-16 sm:h-16 bg-pink-300 rounded-full pulse-slow opacity-70" style="animation-delay: 2s;"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Audio Player Section -->
    <section class="py-20 bg-white relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50"></div>
        <div class="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse" style="animation-delay: 2s;"></div>

        <div class="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16">
                <h2 class="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-4 sm:mb-6">Your Meditation Space</h2>
                <p class="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed px-4">Immerse yourself in our carefully curated collection of calming sounds and guided meditations</p>
            </div>

            <!-- Main Audio Player Card -->
            <div class="bg-white rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-6 lg:p-8 mb-8 sm:mb-12 border border-gray-100 backdrop-blur-sm">
                <!-- Track Info Header -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 space-y-4 sm:space-y-0">
                    <div class="flex items-center space-x-3 sm:space-x-4">
                        <div class="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-water text-white text-lg sm:text-2xl"></i>
                        </div>
                        <div>
                            <h3 id="trackTitle" class="text-lg sm:text-2xl font-bold text-gray-900 mb-1">Harmonics</h3>
                            <p id="trackDescription" class="text-sm sm:text-base text-gray-600">Calming sounds for deep relaxation</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2 sm:space-x-3 justify-center sm:justify-end">
                        <button id="favoriteBtn" class="w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 hover:bg-pink-100 rounded-full flex items-center justify-center transition-all duration-200 group">
                            <i class="fas fa-heart text-gray-400 group-hover:text-pink-500 text-sm sm:text-lg"></i>
                        </button>
                        <button id="shareBtn" class="w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 hover:bg-blue-100 rounded-full flex items-center justify-center transition-all duration-200 group">
                            <i class="fas fa-share text-gray-400 group-hover:text-blue-500 text-sm sm:text-lg"></i>
                        </button>
                    </div>
                </div>

                <!-- Hidden audio element -->
                <audio id="audioPlayer" preload="metadata" loop>
                    <source src="med1.mp3" type="audio/mpeg">
                    Your browser does not support the audio element.
                </audio>

                <!-- Audio Controls -->
                <div class="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:space-x-6">
                    <!-- Play Controls -->
                    <div class="flex items-center justify-center space-x-4 sm:space-x-6">
                        <button id="prevBtn" class="w-12 h-12 sm:w-14 sm:h-14 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full flex items-center justify-center transition-all duration-200 shadow-md hover:shadow-lg">
                            <i class="fas fa-step-backward text-sm sm:text-lg"></i>
                        </button>
                        <button id="playBtn" class="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:scale-105">
                            <i class="fas fa-play text-xl sm:text-2xl ml-1"></i>
                        </button>
                        <button id="nextBtn" class="w-12 h-12 sm:w-14 sm:h-14 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full flex items-center justify-center transition-all duration-200 shadow-md hover:shadow-lg">
                            <i class="fas fa-step-forward text-sm sm:text-lg"></i>
                        </button>
                    </div>

                    <!-- Progress Section -->
                    <div class="flex-1 sm:mx-6">
                        <!-- Meditation Timer Selection -->
                        <div class="mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs sm:text-sm text-gray-600 font-medium">Meditation Duration:</span>
                                <span id="selectedTime" class="text-xs sm:text-sm text-gray-800 font-semibold">∞</span>
                            </div>
                            <div class="flex flex-wrap gap-1 sm:gap-2 mb-3 justify-center sm:justify-start">
                                <button class="time-btn bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-2 sm:px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200" data-time="120">2 min</button>
                                <button class="time-btn bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-2 sm:px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200" data-time="300">5 min</button>
                                <button class="time-btn bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-2 sm:px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200" data-time="600">10 min</button>
                                <button class="time-btn bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-2 sm:px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200" data-time="900">15 min</button>
                                <button class="time-btn bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-2 sm:px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200" data-time="1800">30 min</button>
                                <button class="time-btn bg-indigo-500 text-white px-2 sm:px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200 active" data-time="0">∞</button>
                            </div>
                        </div>

                        <!-- Session Progress (when timer is active) -->
                        <div id="sessionProgress" class="mb-3 hidden">
                            <div class="bg-gradient-to-r from-blue-200 to-purple-200 rounded-full h-3 relative overflow-hidden">
                                <div id="sessionProgressBar" class="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div class="flex justify-between text-sm text-gray-500 font-medium mt-2">
                                <span>Session Progress</span>
                                <span id="remainingTime">0:00</span>
                            </div>
                        </div>

                        <!-- Audio Progress (hidden when session timer is active) -->
                        <div id="audioProgress" class="mb-3">
                            <div id="progressContainer" class="bg-gray-200 rounded-full h-3 cursor-pointer relative overflow-hidden">
                                <div id="progressBar" class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full h-3 w-0 transition-all duration-300 relative">
                                    <div class="absolute right-0 top-0 w-3 h-3 bg-white rounded-full shadow-lg transform translate-x-1/2"></div>
                                </div>
                            </div>
                            <div class="flex justify-between text-sm text-gray-500 font-medium mt-2">
                                <span id="currentTime">0:00</span>
                                <span id="duration">0:00</span>
                            </div>
                        </div>
                    </div>

                    <!-- Volume Controls -->
                    <div class="flex items-center justify-center sm:justify-start space-x-2 sm:space-x-3 mt-4 sm:mt-0">
                        <button id="volumeBtn" class="w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full flex items-center justify-center transition-all duration-200">
                            <i class="fas fa-volume-up text-sm sm:text-lg"></i>
                        </button>
                        <input id="volumeSlider" type="range" min="0" max="100" value="70" class="w-20 sm:w-24 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider">
                    </div>
                </div>
            </div>

            <!-- Track Selection Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                <div class="track-item bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-100 group" data-track="0">
                    <div class="flex items-center space-x-3 sm:space-x-4">
                        <div class="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-water text-white text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-base sm:text-lg font-semibold text-gray-900 mb-1">Harmonics</h4>
                            <p class="text-gray-500 text-xs sm:text-sm">7:45 • Sleep</p>
                        </div>
                    </div>
                </div>

                <div class="track-item bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-100 group" data-track="1">
                    <div class="flex items-center space-x-3 sm:space-x-4">
                        <div class="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-green-400 to-emerald-600 rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-tree text-white text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-base sm:text-lg font-semibold text-gray-900 mb-1">Harmonics</h4>
                            <p class="text-gray-500 text-xs sm:text-sm">10:30 • Sleep</p>
                        </div>
                    </div>
                </div>

                <div class="track-item bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-100 group" data-track="2">
                    <div class="flex items-center space-x-3 sm:space-x-4">
                        <div class="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-lungs text-white text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-base sm:text-lg font-semibold text-gray-900 mb-1">Breathing Exercise</h4>
                            <p class="text-gray-500 text-xs sm:text-sm">5:15 • Stress Relief</p>
                        </div>
                    </div>
                </div>

                <div class="track-item bg-white rounded-xl sm:rounded-2xl p-4 sm:p-6 cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-100 group" data-track="3">
                    <div class="flex items-center space-x-3 sm:space-x-4">
                        <div class="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg sm:rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <i class="fas fa-bullseye text-white text-lg sm:text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-base sm:text-lg font-semibold text-gray-900 mb-1">Focus Session</h4>
                            <p class="text-gray-500 text-xs sm:text-sm">8:20 • Focus</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Meditation Categories -->
    <section id="meditations" class="py-12 sm:py-16 lg:py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16">
                <h2 class="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-4 sm:mb-6">Meditation Categories</h2>
                <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto font-light px-4">Discover the perfect meditation for every moment of your day</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16">
                <!-- Sleep Category -->
                <div class="category-card group bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl sm:rounded-3xl p-6 sm:p-8 text-white hover:scale-105 transition-all duration-500 cursor-pointer shadow-2xl hover:shadow-3xl" data-category="sleep">
                    <div class="w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-full flex items-center justify-center mb-4 sm:mb-6 group-hover:bg-white/30 transition-colors backdrop-blur-sm">
                        <i class="fas fa-moon text-2xl sm:text-3xl"></i>
                    </div>
                    <h3 class="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4 font-display">Sleep</h3>
                    <p class="text-indigo-100 mb-4 sm:mb-6 text-base sm:text-lg font-light leading-relaxed">Drift into peaceful sleep with our bedtime stories and calming soundscapes</p>
                    <div class="flex items-center text-sm font-medium">
                        <span>8 guided lessons</span>
                        <i class="fas fa-arrow-right ml-auto group-hover:translate-x-2 transition-transform text-base sm:text-lg"></i>
                    </div>
                </div>

                <!-- Stress Relief Category -->
                <div class="category-card group bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl sm:rounded-3xl p-6 sm:p-8 text-white hover:scale-105 transition-all duration-500 cursor-pointer shadow-2xl hover:shadow-3xl" data-category="stress">
                    <div class="w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-full flex items-center justify-center mb-4 sm:mb-6 group-hover:bg-white/30 transition-colors backdrop-blur-sm">
                        <i class="fas fa-leaf text-2xl sm:text-3xl"></i>
                    </div>
                    <h3 class="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4 font-display">Stress Relief</h3>
                    <p class="text-emerald-100 mb-4 sm:mb-6 text-base sm:text-lg font-light leading-relaxed">Release tension and find calm in moments of stress and anxiety</p>
                    <div class="flex items-center text-sm font-medium">
                        <span>10 guided lessons</span>
                        <i class="fas fa-arrow-right ml-auto group-hover:translate-x-2 transition-transform text-base sm:text-lg"></i>
                    </div>
                </div>

                <!-- Focus Category -->
                <div class="category-card group bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl sm:rounded-3xl p-6 sm:p-8 text-white hover:scale-105 transition-all duration-500 cursor-pointer shadow-2xl hover:shadow-3xl" data-category="focus">
                    <div class="w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-full flex items-center justify-center mb-4 sm:mb-6 group-hover:bg-white/30 transition-colors backdrop-blur-sm">
                        <i class="fas fa-bullseye text-2xl sm:text-3xl"></i>
                    </div>
                    <h3 class="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4 font-display">Focus</h3>
                    <p class="text-orange-100 mb-4 sm:mb-6 text-base sm:text-lg font-light leading-relaxed">Enhance concentration and mental clarity for work and study</p>
                    <div class="flex items-center text-sm font-medium">
                        <span>6 guided lessons</span>
                        <i class="fas fa-arrow-right ml-auto group-hover:translate-x-2 transition-transform text-base sm:text-lg"></i>
                    </div>
                </div>
            </div>

            <!-- Lessons Container -->
            <div id="lessonsContainer" class="hidden">
                <div class="bg-white rounded-3xl shadow-2xl p-8 backdrop-blur-lg border border-gray-100">
                    <div class="flex items-center justify-between mb-8">
                        <h3 id="categoryTitle" class="text-4xl font-display font-bold text-gray-900"></h3>
                        <button id="closeLessons" class="w-12 h-12 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-colors">
                            <i class="fas fa-times text-gray-600"></i>
                        </button>
                    </div>
                    <div id="lessonsGrid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Benefits will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-12 sm:py-16 lg:py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16">
                <h2 class="text-3xl sm:text-4xl font-display font-bold text-gray-900 mb-4">Why Choose MindSpace?</h2>
                <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto px-4">Professional-grade meditation tools trusted by millions worldwide</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
                <div class="text-center group">
                    <div class="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-indigo-200 transition-colors">
                        <i class="fas fa-brain text-indigo-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Science-Based</h3>
                    <p class="text-gray-600">All our meditations are backed by neuroscience research and proven techniques</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-emerald-200 transition-colors">
                        <i class="fas fa-users text-emerald-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Expert Teachers</h3>
                    <p class="text-gray-600">Learn from world-renowned meditation masters and certified instructors</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-orange-200 transition-colors">
                        <i class="fas fa-mobile-alt text-orange-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Anywhere Access</h3>
                    <p class="text-gray-600">Meditate on any device, online or offline, wherever you are</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-200 transition-colors">
                        <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Track Progress</h3>
                    <p class="text-gray-600">Monitor your meditation journey with detailed insights and achievements</p>
                </div>
            </div>
        </div>
    </section>



    <!-- About Section -->
    <section id="about" class="py-12 sm:py-16 lg:py-20 bg-gradient-to-br from-indigo-50 via-white to-purple-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 sm:mb-16">
                <h2 class="text-3xl sm:text-4xl lg:text-5xl font-display font-bold text-gray-900 mb-4 sm:mb-6">About MindSpace</h2>
                <p class="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto font-light px-4">Your sanctuary for inner peace and mindful living</p>
            </div>

            <div class="grid lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 items-center mb-12 sm:mb-16 lg:mb-20">
                <div>
                    <h3 class="text-3xl font-display font-bold text-gray-900 mb-6">Our Mission</h3>
                    <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                        At MindSpace, we believe that everyone deserves access to inner peace and mental clarity. Our mission is to make meditation accessible, enjoyable, and transformative for people from all walks of life.
                    </p>
                    <p class="text-lg text-gray-600 mb-8 leading-relaxed">
                        Through carefully curated meditation sessions, peaceful games, and mindfulness tools, we create a digital sanctuary where you can escape the chaos of daily life and reconnect with your inner self.
                    </p>
                    <div class="text-center">
                        <div class="bg-gradient-to-r from-indigo-100 to-purple-100 rounded-2xl p-6">
                            <div class="text-lg font-semibold text-gray-800 mb-2">Crafted with Care</div>
                            <div class="text-gray-600">Done by Nandini</div>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <div class="w-full h-96 bg-gradient-to-br from-indigo-400 to-purple-600 rounded-3xl flex items-center justify-center text-white shadow-2xl">
                        <div class="text-center">
                            <i class="fas fa-om text-6xl mb-4 opacity-80"></i>
                            <h4 class="text-2xl font-display font-bold">Find Your Inner Peace</h4>
                        </div>
                    </div>
                    <div class="absolute -top-6 -right-6 w-24 h-24 bg-yellow-300 rounded-full opacity-70 animate-pulse"></div>
                    <div class="absolute -bottom-6 -left-6 w-20 h-20 bg-pink-300 rounded-full opacity-70 animate-pulse" style="animation-delay: 1s;"></div>
                </div>
            </div>

            <div class="grid md:grid-cols-3 gap-8 mb-16">
                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg">
                        <i class="fas fa-heart text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-900 mb-4">Mindful Approach</h4>
                    <p class="text-gray-600">Every session is designed with care and intention to nurture your mental wellbeing</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg">
                        <i class="fas fa-leaf text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-900 mb-4">Natural Healing</h4>
                    <p class="text-gray-600">Harness the power of nature sounds and peaceful environments for deep relaxation</p>
                </div>

                <div class="text-center group">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform shadow-lg">
                        <i class="fas fa-infinity text-white text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-900 mb-4">Endless Journey</h4>
                    <p class="text-gray-600">Your path to inner peace is unique and ever-evolving, just like you</p>
                </div>
            </div>

            <div class="bg-white rounded-2xl sm:rounded-3xl shadow-2xl p-6 sm:p-8 text-center">
                <h3 class="text-xl sm:text-2xl font-display font-bold text-gray-900 mb-3 sm:mb-4">Find Your Inner Peace</h3>
                <p class="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">
                    Begin your meditation journey today.
                </p>
            </div>
        </div>
    </section>

    <!-- Chatbot Modal -->
    <div id="chatbotModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-2 sm:p-4" onclick="if(event.target === this) document.getElementById('chatbotModal').classList.add('hidden')">
        <div class="bg-white rounded-2xl sm:rounded-3xl shadow-2xl w-full max-w-2xl h-[90vh] sm:h-[600px] flex flex-col overflow-hidden">
            <!-- Chatbot Header -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 sm:p-6 text-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3 sm:space-x-4">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-lg sm:text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg sm:text-xl font-bold">MindSpace AI Assistant</h3>
                            <p class="text-indigo-100 text-xs sm:text-sm">Your personal meditation guide</p>
                        </div>
                    </div>
                    <button id="closeChatbot" onclick="document.getElementById('chatbotModal').classList.add('hidden')" class="w-8 h-8 sm:w-10 sm:h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors">
                        <i class="fas fa-times text-sm sm:text-base"></i>
                    </button>
                </div>
            </div>

            <!-- Chat Messages Container -->
            <div id="chatMessages" class="flex-1 p-3 sm:p-6 overflow-y-auto bg-gray-50">
                <div class="space-y-3 sm:space-y-4">
                    <!-- Welcome Message -->
                    <div class="flex items-start space-x-2 sm:space-x-3">
                        <div class="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-robot text-white text-xs sm:text-sm"></i>
                        </div>
                        <div class="bg-white rounded-2xl rounded-tl-sm p-3 sm:p-4 shadow-sm max-w-xs sm:max-w-sm">
                            <p class="text-gray-800 text-sm sm:text-base">Hello! I'm your MindSpace AI assistant. I'm here to help you with meditation guidance, stress relief tips, and mindfulness practices. How can I support your wellness journey today?</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-3 sm:p-6 bg-white border-t border-gray-200">
                <div class="flex space-x-2 sm:space-x-4">
                    <input
                        id="chatInput"
                        type="text"
                        placeholder="Ask me about meditation..."
                        onkeypress="if(event.key==='Enter') sendChatMessage()"
                        class="flex-1 px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-xl sm:rounded-2xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-sm sm:text-base"
                    >
                    <button
                        id="sendMessage"
                        onclick="sendChatMessage()"
                        class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-3 sm:px-6 py-2 sm:py-3 rounded-xl sm:rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-1 sm:space-x-2"
                    >
                        <i class="fas fa-paper-plane text-sm"></i>
                        <span class="hidden sm:inline">Send</span>
                    </button>
                </div>
                <div class="mt-3 flex flex-wrap gap-2">
                    <button onclick="askQuickQuestion('How do I start meditating?')" class="quick-question bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-3 py-1 rounded-full text-sm transition-colors">
                        How do I start meditating?
                    </button>
                    <button onclick="askQuickQuestion('Help me with stress relief')" class="quick-question bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-3 py-1 rounded-full text-sm transition-colors">
                        Help me with stress relief
                    </button>
                    <button onclick="askQuickQuestion('Best meditation for sleep')" class="quick-question bg-gray-100 hover:bg-indigo-100 text-gray-700 hover:text-indigo-700 px-3 py-1 rounded-full text-sm transition-colors">
                        Best meditation for sleep
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-6 sm:py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h3 class="text-xl sm:text-2xl font-bold gradient-bg bg-clip-text text-transparent mb-3 sm:mb-4">MindSpace</h3>
                <p class="text-gray-400 mb-4 sm:mb-6 text-sm sm:text-base px-4">Your journey to inner peace and mindfulness starts here.</p>
                <div class="border-t border-gray-800 mt-6 sm:mt-8 pt-4 sm:pt-6 text-gray-400">
                    <p class="text-xs sm:text-sm">&copy; 2024 MindSpace. All rights reserved. Made with ❤️ for your wellbeing.</p>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Authentication check - MUST BE FIRST
        document.addEventListener('DOMContentLoaded', function() {
            const currentUser = localStorage.getItem('mindspace_current_user');
            if (!currentUser) {
                window.location.href = 'auth.html';
                return;
            }

            // Display user welcome message
            const user = JSON.parse(currentUser);
            const welcomeElement = document.getElementById('userWelcome');
            const mobileWelcomeElement = document.getElementById('mobileUserWelcome');

            if (welcomeElement) {
                welcomeElement.textContent = `Welcome back, ${user.name}!`;
            }
            if (mobileWelcomeElement) {
                mobileWelcomeElement.textContent = `Welcome back, ${user.name}!`;
            }
        });

        // Logout function
        function logout() {
            localStorage.removeItem('mindspace_current_user');
            window.location.href = 'auth.html';
        }

        // Define all chatbot functions first - MUST BE AT TOP
        function openChatbotModal() {
            const chatbotModal = document.getElementById('chatbotModal');
            if (chatbotModal) {
                chatbotModal.classList.remove('hidden');
                const chatInput = document.getElementById('chatInput');
                if (chatInput) {
                    setTimeout(() => chatInput.focus(), 100);
                }
            }
        }

        function closeChatbotModal() {
            const chatbotModal = document.getElementById('chatbotModal');
            if (chatbotModal) {
                chatbotModal.classList.add('hidden');
            }
        }

        // Add loading state management
        let isProcessing = false;

        async function sendChatMessage() {
            if (isProcessing) return; // Prevent multiple simultaneous requests
            
            const chatInput = document.getElementById('chatInput');
            const chatMessages = document.getElementById('chatMessages');

            if (chatInput && chatInput.value.trim()) {
                const userMessage = chatInput.value.trim();
                
                // Set processing state
                isProcessing = true;
                
                // Add user message to chat
                addMessageToChat('user', userMessage);
                
                // Clear input and disable it
                chatInput.value = '';
                chatInput.disabled = true;
                
                // Show typing indicator
                const typingId = addTypingIndicator();

                try {
                    const aiResponse = await generateAIResponse(userMessage);
                    removeTypingIndicator(typingId);
                    addMessageToChat('ai', aiResponse);
                } catch (error) {
                    console.error('Chat error:', error);
                    removeTypingIndicator(typingId);
                    addMessageToChat('ai', "I'm having trouble connecting right now. Please try again in a moment.");
                } finally {
                    // Re-enable input and reset processing state
                    chatInput.disabled = false;
                    chatInput.focus();
                    isProcessing = false;
                }
            }
        }

        function askQuickQuestion(question) {
            if (isProcessing) return; // Prevent if already processing
            
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.value = question;
                sendChatMessage();
            }
        }

        function addTypingIndicator() {
            const chatMessages = document.getElementById('chatMessages');
            if (!chatMessages) return null;

            const typingDiv = document.createElement('div');
            const typingId = 'typing-' + Date.now();
            typingDiv.id = typingId;
            typingDiv.className = 'flex items-start space-x-3';
            
            typingDiv.innerHTML = `
                <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <div class="bg-white rounded-2xl rounded-tl-sm p-4 shadow-sm">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                </div>
            `;
            
            chatMessages.appendChild(typingDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            return typingId;
        }

        function removeTypingIndicator(typingId) {
            if (typingId) {
                const typingElement = document.getElementById(typingId);
                if (typingElement) {
                    typingElement.remove();
                }
            }
        }

        function addMessageToChat(sender, message) {
            const chatMessages = document.getElementById('chatMessages');
            if (!chatMessages) return;

            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 mb-4';

            // Escape HTML to prevent XSS
            const escapedMessage = escapeHtml(message);

            if (sender === 'user') {
                messageDiv.className += ' justify-end';
                messageDiv.innerHTML = `
                    <div class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-2xl rounded-tr-sm p-4 shadow-sm max-w-xs">
                        <p>${escapedMessage}</p>
                    </div>
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-gray-600 text-sm"></i>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="bg-white rounded-2xl rounded-tl-sm p-4 shadow-sm max-w-xs">
                        <p class="text-gray-800">${escapedMessage}</p>
                    </div>
                `;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Utility function to escape HTML
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        async function generateAIResponse(message) {
            const apiKey = "AIzaSyBy1_TSTqljYIhBfsePA0PSGx1jHqeO6Jc";
            const url = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`;

            const headers = {
                "Content-Type": "application/json"
            };

            const payload = {
                contents: [{
                    parts: [{
                        text: `You are a therapy assistant providing supportive and empathetic responses to users. Keep responses under 150 words and focus on being helpful and understanding.\n\nUser: ${message}`
                    }]
                }],
                generationConfig: {
                    maxOutputTokens: 150,
                    temperature: 0.7
                }
            };

            try {
                const response = await fetch(url, {
                    method: "POST",
                    headers: headers,
                    body: JSON.stringify(payload)
                });

                // Check if response is ok
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API Error:', response.status, errorText);
                    throw new Error(`API Error: ${response.status}`);
                }

                const data = await response.json();
                
                // More robust response parsing
                if (data.candidates && 
                    data.candidates[0] && 
                    data.candidates[0].content && 
                    data.candidates[0].content.parts && 
                    data.candidates[0].content.parts[0] && 
                    data.candidates[0].content.parts[0].text) {
                    
                    return data.candidates[0].content.parts[0].text.trim();
                } else {
                    console.error('Unexpected response format:', data);
                    throw new Error('Unexpected response format');
                }
                
            } catch (error) {
                console.error("Error generating AI response:", error);
                throw error; // Re-throw to be handled by calling function
            }
        }

        // Add Enter key support for chat input
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendChatMessage();
                    }
                });
            }
        });

        // Test that functions are loaded
        console.log('Chatbot functions loaded:', {
            sendChatMessage: typeof sendChatMessage,
            askQuickQuestion: typeof askQuickQuestion,
            openChatbotModal: typeof openChatbotModal,
            closeChatbotModal: typeof closeChatbotModal
        });


        // Enhanced MP3 audio player functionality
        const audioPlayer = document.getElementById('audioPlayer');
        const playBtn = document.getElementById('playBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const progressBar = document.getElementById('progressBar');
        const progressContainer = document.getElementById('progressContainer');
        const currentTimeDisplay = document.getElementById('currentTime');
        const durationDisplay = document.getElementById('duration');
        const volumeBtn = document.getElementById('volumeBtn');
        const volumeSlider = document.getElementById('volumeSlider');
        const trackTitle = document.getElementById('trackTitle');
        const trackDescription = document.getElementById('trackDescription');

        // Meditation tracks data with MP3 files
        const tracks = [
            {
                title: "Meditation 1",
                description: "Calming meditation for deep relaxation",
                src: "med1.mp3",
                category: "sleep"
            },
            {
                title: "Meditation 2",
                description: "Peaceful meditation for mindfulness",
                src: "med2.mp3",
                category: "sleep"
            },
            {
                title: "Blue Meditation",
                description: "Soothing blue meditation for stress relief",
                src: "meditation-blue-138131.mp3",
                category: "stress-relief"
            },
            {
                title: "Water Sounds",
                description: "Relaxing water sounds for focus and clarity",
                src: "water-relaxing-sound-121599.mp3",
                category: "focus"
            }
        ];

        let currentTrack = 0;
        let isPlaying = false;
        let meditationTimer = 0; // 0 means infinite
        let sessionStartTime = null;
        let sessionInterval = null;

        // Initialize audio player
        function initializePlayer() {
            audioPlayer.volume = volumeSlider.value / 100;
            loadTrack(currentTrack);
        }

        // Load track
        function loadTrack(trackIndex) {
            const track = tracks[trackIndex];
            audioPlayer.src = track.src;
            trackTitle.textContent = track.title;
            trackDescription.textContent = track.description;

            // Update track icon in main player
            const mainIcon = document.querySelector('.w-16.h-16 i');
            const trackIcons = ['fa-water', 'fa-tree', 'fa-lungs', 'fa-bullseye'];
            const trackColors = [
                'from-blue-500 to-indigo-600',
                'from-green-400 to-emerald-600',
                'from-purple-400 to-purple-600',
                'from-orange-400 to-red-500'
            ];

            // Update main player icon
            mainIcon.className = `fas ${trackIcons[trackIndex]} text-white text-2xl`;
            const iconContainer = mainIcon.parentElement;
            iconContainer.className = `w-16 h-16 bg-gradient-to-br ${trackColors[trackIndex]} rounded-2xl flex items-center justify-center shadow-lg`;

            // Update active track visual
            document.querySelectorAll('.track-item').forEach((item, index) => {
                if (index === trackIndex) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        }

        // Play/Pause functionality
        playBtn.addEventListener('click', function() {
            if (isPlaying) {
                audioPlayer.pause();
            } else {
                audioPlayer.play();
            }
        });

        // Audio event listeners
        audioPlayer.addEventListener('play', function() {
            isPlaying = true;
            playBtn.innerHTML = '<i class="fas fa-pause text-xl"></i>';
            playBtn.classList.add('breathing', 'glow');
            startSession();
        });

        audioPlayer.addEventListener('pause', function() {
            isPlaying = false;
            playBtn.innerHTML = '<i class="fas fa-play text-xl ml-1"></i>';
            playBtn.classList.remove('breathing', 'glow');
            if (sessionInterval) {
                clearInterval(sessionInterval);
                sessionInterval = null;
            }
        });

        audioPlayer.addEventListener('timeupdate', function() {
            if (audioPlayer.duration) {
                const progress = (audioPlayer.currentTime / audioPlayer.duration) * 100;
                progressBar.style.width = progress + '%';
                currentTimeDisplay.textContent = formatTime(audioPlayer.currentTime);
            }
        });

        audioPlayer.addEventListener('loadedmetadata', function() {
            durationDisplay.textContent = formatTime(audioPlayer.duration);
        });

        audioPlayer.addEventListener('ended', function() {
            nextTrack();
        });

        // Error handling for missing MP3 files
        audioPlayer.addEventListener('error', function() {
            console.log('Audio file not found:', tracks[currentTrack].src);
            trackDescription.textContent = 'Audio file not found. Please add MP3 file to sounds folder.';
            playBtn.innerHTML = '<i class="fas fa-exclamation-triangle text-xl"></i>';
            playBtn.disabled = true;
            setTimeout(() => {
                playBtn.innerHTML = '<i class="fas fa-play text-xl ml-1"></i>';
                playBtn.disabled = false;
                trackDescription.textContent = tracks[currentTrack].description;
            }, 3000);
        });

        // Previous/Next track
        prevBtn.addEventListener('click', function() {
            currentTrack = (currentTrack - 1 + tracks.length) % tracks.length;
            loadTrack(currentTrack);
        });

        nextBtn.addEventListener('click', function() {
            nextTrack();
        });

        function nextTrack() {
            currentTrack = (currentTrack + 1) % tracks.length;
            loadTrack(currentTrack);
        }

        // Progress bar click
        progressContainer.addEventListener('click', function(e) {
            if (audioPlayer.duration) {
                const rect = progressContainer.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                const width = rect.width;
                const clickTime = (clickX / width) * audioPlayer.duration;
                audioPlayer.currentTime = clickTime;
            }
        });

        // Volume control
        volumeSlider.addEventListener('input', function() {
            audioPlayer.volume = this.value / 100;
            updateVolumeIcon();
            updateVolumeSlider();
        });

        volumeBtn.addEventListener('click', function() {
            if (audioPlayer.volume > 0) {
                audioPlayer.volume = 0;
                volumeSlider.value = 0;
            } else {
                audioPlayer.volume = 0.7;
                volumeSlider.value = 70;
            }
            updateVolumeIcon();
            updateVolumeSlider();
        });

        function updateVolumeIcon() {
            const volume = audioPlayer.volume;
            const icon = volumeBtn.querySelector('i');
            if (volume === 0) {
                icon.className = 'fas fa-volume-mute text-lg';
            } else if (volume < 0.5) {
                icon.className = 'fas fa-volume-down text-lg';
            } else {
                icon.className = 'fas fa-volume-up text-lg';
            }
        }

        function updateVolumeSlider() {
            const value = volumeSlider.value;
            volumeSlider.style.background = `linear-gradient(to right, #6366f1 0%, #6366f1 ${value}%, #e5e7eb ${value}%, #e5e7eb 100%)`;
        }

        // Track selection
        document.querySelectorAll('.track-item').forEach((item, index) => {
            item.addEventListener('click', function() {
                currentTrack = index;
                resetSession();
                loadTrack(currentTrack);
                audioPlayer.play();
            });
        });

        function formatTime(seconds) {
            if (isNaN(seconds)) return '0:00';
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        // Timer functionality
        function initializeTimer() {
            const timeButtons = document.querySelectorAll('.time-btn');
            const selectedTimeDisplay = document.getElementById('selectedTime');
            const sessionProgress = document.getElementById('sessionProgress');
            const audioProgress = document.getElementById('audioProgress');
            const sessionProgressBar = document.getElementById('sessionProgressBar');
            const remainingTimeDisplay = document.getElementById('remainingTime');

            timeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    timeButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-indigo-500', 'text-white');
                        btn.classList.add('bg-gray-100', 'hover:bg-indigo-100', 'text-gray-700', 'hover:text-indigo-700');
                    });

                    // Add active class to clicked button
                    this.classList.add('active', 'bg-indigo-500', 'text-white');
                    this.classList.remove('bg-gray-100', 'hover:bg-indigo-100', 'text-gray-700', 'hover:text-indigo-700');

                    // Set meditation timer
                    meditationTimer = parseInt(this.dataset.time);

                    // Update display and progress bars
                    if (meditationTimer === 0) {
                        selectedTimeDisplay.textContent = '∞';
                        sessionProgress.classList.add('hidden');
                        audioProgress.classList.remove('hidden');
                    } else {
                        const minutes = Math.floor(meditationTimer / 60);
                        selectedTimeDisplay.textContent = `${minutes} min`;
                        sessionProgress.classList.remove('hidden');
                        audioProgress.classList.add('hidden');
                    }

                    // Reset session if playing
                    if (isPlaying) {
                        resetSession();
                        startSession();
                    }
                });
            });
        }

        function startSession() {
            if (meditationTimer > 0) {
                sessionStartTime = Date.now();
                sessionInterval = setInterval(updateSessionProgress, 1000);
                document.getElementById('sessionProgress').classList.remove('hidden');
                document.getElementById('audioProgress').classList.add('hidden');
            } else {
                // Infinite mode - show audio progress
                document.getElementById('sessionProgress').classList.add('hidden');
                document.getElementById('audioProgress').classList.remove('hidden');
            }
        }

        function resetSession() {
            if (sessionInterval) {
                clearInterval(sessionInterval);
                sessionInterval = null;
            }
            sessionStartTime = null;
            document.getElementById('sessionProgressBar').style.width = '0%';
            document.getElementById('remainingTime').textContent = formatTime(meditationTimer);
        }

        function updateSessionProgress() {
            if (!sessionStartTime || meditationTimer === 0) return;

            const elapsed = (Date.now() - sessionStartTime) / 1000;
            const progress = (elapsed / meditationTimer) * 100;
            const remaining = Math.max(0, meditationTimer - elapsed);

            document.getElementById('sessionProgressBar').style.width = Math.min(100, progress) + '%';
            document.getElementById('remainingTime').textContent = formatTime(remaining);

            // End session when timer reaches zero
            if (elapsed >= meditationTimer) {
                endSession();
            }
        }

        function endSession() {
            audioPlayer.pause();
            resetSession();

            // Show completion message
            const completionMessage = document.createElement('div');
            completionMessage.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
            completionMessage.innerHTML = `
                <div class="flex items-center space-x-2">
                    <i class="fas fa-check-circle"></i>
                    <span>Meditation session complete! 🧘‍♀️</span>
                </div>
            `;
            document.body.appendChild(completionMessage);

            // Animate in
            setTimeout(() => {
                completionMessage.classList.remove('translate-x-full');
            }, 100);

            // Remove after 4 seconds
            setTimeout(() => {
                completionMessage.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(completionMessage);
                }, 300);
            }, 4000);
        }

        // Meditation category information and benefits
        const meditationInfo = {
            sleep: {
                title: "Sleep Meditation Benefits",
                subtitle: "Why Quality Sleep Meditation Transforms Your Life",
                benefits: [
                    {
                        title: "Deeper, More Restful Sleep",
                        description: "Sleep meditation helps quiet the mind and relax the body, leading to faster sleep onset and deeper sleep cycles",
                        icon: "fa-bed",
                        color: "from-blue-500 to-indigo-600"
                    },
                    {
                        title: "Reduced Nighttime Anxiety",
                        description: "Calms racing thoughts and worries that often keep us awake, creating a peaceful mental state for rest",
                        icon: "fa-moon",
                        color: "from-purple-500 to-pink-600"
                    },
                    {
                        title: "Better Sleep Quality",
                        description: "Improves REM sleep and deep sleep phases, leading to more refreshing and restorative rest",
                        icon: "fa-star",
                        color: "from-indigo-500 to-purple-600"
                    },
                    {
                        title: "Natural Sleep Rhythm",
                        description: "Helps regulate your circadian rhythm and establish healthy sleep patterns without medication",
                        icon: "fa-clock",
                        color: "from-teal-500 to-blue-600"
                    },
                    {
                        title: "Morning Energy Boost",
                        description: "Wake up feeling more refreshed, energized, and ready to tackle the day with clarity",
                        icon: "fa-sun",
                        color: "from-yellow-500 to-orange-600"
                    },
                    {
                        title: "Stress-Free Bedtime",
                        description: "Transform bedtime from a source of stress into a peaceful, anticipated part of your day",
                        icon: "fa-heart",
                        color: "from-pink-500 to-red-600"
                    }
                ]
            },
            stress: {
                title: "Stress Relief Through Meditation",
                subtitle: "How Meditation Naturally Reduces Stress & Anxiety",
                benefits: [
                    {
                        title: "Instant Calm & Relaxation",
                        description: "Activates the body's relaxation response, immediately reducing cortisol levels and tension",
                        icon: "fa-leaf",
                        color: "from-green-500 to-emerald-600"
                    },
                    {
                        title: "Emotional Regulation",
                        description: "Develops the ability to observe emotions without being overwhelmed, creating emotional stability",
                        icon: "fa-balance-scale",
                        color: "from-blue-500 to-teal-600"
                    },
                    {
                        title: "Lower Blood Pressure",
                        description: "Regular practice naturally reduces blood pressure and heart rate, promoting cardiovascular health",
                        icon: "fa-heartbeat",
                        color: "from-red-500 to-pink-600"
                    },
                    {
                        title: "Mental Clarity",
                        description: "Clears mental fog and overwhelm, helping you think more clearly and make better decisions",
                        icon: "fa-lightbulb",
                        color: "from-yellow-500 to-orange-600"
                    },
                    {
                        title: "Improved Resilience",
                        description: "Builds mental strength and resilience to handle life's challenges with greater ease and confidence",
                        icon: "fa-mountain",
                        color: "from-gray-500 to-blue-600"
                    },
                    {
                        title: "Better Relationships",
                        description: "Reduces reactivity and increases patience, leading to more harmonious relationships with others",
                        icon: "fa-users",
                        color: "from-purple-500 to-indigo-600"
                    },
                    {
                        title: "Physical Tension Release",
                        description: "Releases muscle tension and physical stress stored in the body, promoting overall relaxation",
                        icon: "fa-user-check",
                        color: "from-teal-500 to-green-600"
                    }
                ]
            },
            focus: {
                title: "Focus & Mental Clarity",
                subtitle: "Enhance Concentration & Cognitive Performance",
                benefits: [
                    {
                        title: "Enhanced Concentration",
                        description: "Strengthens your ability to maintain attention on tasks for longer periods without distraction",
                        icon: "fa-bullseye",
                        color: "from-orange-500 to-red-600"
                    },
                    {
                        title: "Improved Memory",
                        description: "Enhances working memory and information retention, making learning and recall more efficient",
                        icon: "fa-brain",
                        color: "from-purple-500 to-pink-600"
                    },
                    {
                        title: "Mental Agility",
                        description: "Increases cognitive flexibility and the ability to switch between different tasks and concepts",
                        icon: "fa-rocket",
                        color: "from-blue-500 to-indigo-600"
                    },
                    {
                        title: "Creative Problem Solving",
                        description: "Opens the mind to new perspectives and innovative solutions to challenges",
                        icon: "fa-palette",
                        color: "from-pink-500 to-purple-600"
                    },
                    {
                        title: "Reduced Mental Fatigue",
                        description: "Prevents mental burnout and maintains cognitive energy throughout the day",
                        icon: "fa-battery-full",
                        color: "from-green-500 to-teal-600"
                    },
                    {
                        title: "Better Decision Making",
                        description: "Improves clarity of thought and the ability to make wise, well-considered decisions",
                        icon: "fa-chess",
                        color: "from-gray-500 to-blue-600"
                    }
                ]
            }
        };

        // Category interaction functionality
        function initializeCategoryInteraction() {
            const categoryCards = document.querySelectorAll('.category-card');
            const lessonsContainer = document.getElementById('lessonsContainer');
            const categoryTitle = document.getElementById('categoryTitle');
            const lessonsGrid = document.getElementById('lessonsGrid');
            const closeLessons = document.getElementById('closeLessons');

            categoryCards.forEach(card => {
                card.addEventListener('click', function() {
                    const category = this.dataset.category;
                    showLessons(category);
                });
            });

            closeLessons.addEventListener('click', function() {
                hideLessons();
            });

            function showLessons(category) {
                const categoryData = meditationInfo[category];

                categoryTitle.textContent = categoryData.title;
                lessonsGrid.innerHTML = '';

                // Add subtitle
                const subtitle = document.createElement('div');
                subtitle.className = 'col-span-full text-center mb-8';
                subtitle.innerHTML = `
                    <p class="text-xl text-gray-600 font-light italic">${categoryData.subtitle}</p>
                `;
                lessonsGrid.appendChild(subtitle);

                categoryData.benefits.forEach((benefit, index) => {
                    const benefitCard = document.createElement('div');
                    benefitCard.className = 'lesson-card bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-indigo-200 group cursor-pointer';
                    benefitCard.innerHTML = `
                        <div class="flex items-center mb-4">
                            <div class="w-14 h-14 bg-gradient-to-br ${benefit.color} rounded-xl flex items-center justify-center text-white shadow-lg group-hover:scale-110 transition-transform">
                                <i class="fas ${benefit.icon} text-lg"></i>
                            </div>
                        </div>
                        <h4 class="text-xl font-bold text-gray-900 mb-3 font-display group-hover:text-indigo-700 transition-colors">${benefit.title}</h4>
                        <p class="text-gray-600 text-sm leading-relaxed font-light mb-4">${benefit.description}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-indigo-600 font-semibold group-hover:translate-x-1 transition-transform">
                                <i class="fas fa-search mr-2"></i>
                                Find Guided Meditations
                            </div>
                            <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center group-hover:bg-indigo-200 transition-colors">
                                <i class="fas fa-external-link-alt text-indigo-600 text-xs"></i>
                            </div>
                        </div>
                        <div class="mt-3 flex items-center">
                            <div class="w-full bg-gray-200 rounded-full h-1">
                                <div class="bg-gradient-to-r ${benefit.color} h-1 rounded-full transition-all duration-1000 group-hover:w-full" style="width: 60%"></div>
                            </div>
                        </div>
                    `;

                    // Add click event to open Google search
                    benefitCard.addEventListener('click', function() {
                        openGuidedMeditationSearch(benefit, category);
                    });

                    lessonsGrid.appendChild(benefitCard);
                });

                lessonsContainer.classList.remove('hidden');
                lessonsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }

            function hideLessons() {
                lessonsContainer.classList.add('hidden');
            }

            function openGuidedMeditationSearch(benefit, category) {
                // Create specific search queries for each benefit
                const searchQueries = {
                    sleep: {
                        "Deeper, More Restful Sleep": "guided meditation for deep sleep relaxation bedtime",
                        "Reduced Nighttime Anxiety": "guided meditation for sleep anxiety calming bedtime",
                        "Better Sleep Quality": "guided meditation improve sleep quality REM deep sleep",
                        "Natural Sleep Rhythm": "guided meditation circadian rhythm natural sleep cycle",
                        "Morning Energy Boost": "guided meditation wake up energized morning routine",
                        "Stress-Free Bedtime": "guided meditation peaceful bedtime routine stress relief"
                    },
                    stress: {
                        "Instant Calm & Relaxation": "guided meditation instant calm stress relief relaxation",
                        "Emotional Regulation": "guided meditation emotional balance mindfulness stress",
                        "Lower Blood Pressure": "guided meditation lower blood pressure heart health",
                        "Mental Clarity": "guided meditation mental clarity clear mind focus",
                        "Improved Resilience": "guided meditation build resilience mental strength",
                        "Better Relationships": "guided meditation loving kindness compassion relationships",
                        "Physical Tension Release": "guided meditation body scan muscle tension release"
                    },
                    focus: {
                        "Enhanced Concentration": "guided meditation concentration focus attention training",
                        "Improved Memory": "guided meditation improve memory cognitive function",
                        "Mental Agility": "guided meditation mental agility cognitive flexibility",
                        "Creative Problem Solving": "guided meditation creativity innovation problem solving",
                        "Reduced Mental Fatigue": "guided meditation mental energy cognitive stamina",
                        "Better Decision Making": "guided meditation clarity decision making wisdom"
                    }
                };

                // Get the search query for this specific benefit
                const searchQuery = searchQueries[category][benefit.title] || `guided meditation ${benefit.title.toLowerCase()}`;

                // Create the Google search URL
                const googleSearchUrl = `https://www.google.com/search?q=${encodeURIComponent(searchQuery)}`;

                // Show a beautiful notification before opening
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-gradient-to-r from-indigo-500 to-purple-600 text-white px-6 py-4 rounded-2xl shadow-2xl z-50 transform translate-x-full transition-transform duration-500';
                notification.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="fas ${benefit.icon}"></i>
                        </div>
                        <div>
                            <div class="font-semibold">${benefit.title}</div>
                            <div class="text-sm opacity-90">Finding guided meditations...</div>
                        </div>
                    </div>
                `;
                document.body.appendChild(notification);

                // Animate in
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Open Google search in new tab after a short delay
                setTimeout(() => {
                    window.open(googleSearchUrl, '_blank');
                }, 800);

                // Remove notification after 3 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (document.body.contains(notification)) {
                            document.body.removeChild(notification);
                        }
                    }, 500);
                }, 3000);
            }
        }

        // Chatbot functionality with free AI service
        const HUGGINGFACE_API_URL = 'https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium';

        function initializeChatbot() {
            const chatbotBtn = document.getElementById('chatbotBtn');
            const mobileChatbotBtn = document.getElementById('mobileChatbotBtn');
            const chatbotModal = document.getElementById('chatbotModal');
            const closeChatbot = document.getElementById('closeChatbot');
            const chatInput = document.getElementById('chatInput');
            const sendMessage = document.getElementById('sendMessage');
            const quickQuestionButtons = document.querySelectorAll('.quick-question');

            // Open chatbot
            function openChatbot(e) {
                e.preventDefault();
                e.stopPropagation();
                if (chatbotModal) {
                    chatbotModal.classList.remove('hidden');
                    if (chatInput) {
                        setTimeout(() => chatInput.focus(), 100);
                    }
                }
            }

            // Close chatbot
            function closeChatbotModal() {
                if (chatbotModal) {
                    chatbotModal.classList.add('hidden');
                }
            }

            // Event listeners with error checking
            if (chatbotBtn) {
                chatbotBtn.addEventListener('click', openChatbot);
            }
            if (mobileChatbotBtn) {
                mobileChatbotBtn.addEventListener('click', openChatbot);
            }
            if (closeChatbot) {
                closeChatbot.addEventListener('click', closeChatbotModal);
            }

            // Close on backdrop click
            if (chatbotModal) {
                chatbotModal.addEventListener('click', function(e) {
                    if (e.target === chatbotModal) {
                        closeChatbotModal();
                    }
                });
            }

            // Send message on Enter key
            if (chatInput) {
                chatInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendChatMessage();
                    }
                });
            }

            // Send message on button click
            if (sendMessage) {
                sendMessage.addEventListener('click', sendChatMessage);
            }

            // Quick questions
            if (quickQuestionButtons) {
                quickQuestionButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const question = btn.getAttribute('onclick').match(/'([^']+)'/)[1];
                        if (chatInput) {
                            chatInput.value = question;
                            sendChatMessage();
                        }
                    });
                });
            }
        }

        // Initialize chatbot on page load
        document.addEventListener('DOMContentLoaded', initializeChatbot);

        // Initialize player when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializePlayer();
            initializeTimer();
            initializeCategoryInteraction();
            initializeChatbot();

            // Additional chatbot button setup as backup
            setTimeout(() => {
                const chatbotBtn = document.getElementById('chatbotBtn');
                const mobileChatbotBtn = document.getElementById('mobileChatbotBtn');
                const chatbotModal = document.getElementById('chatbotModal');

                if (chatbotBtn && !chatbotBtn.hasAttribute('data-listener')) {
                    chatbotBtn.addEventListener('click', function() {
                        if (chatbotModal) {
                            chatbotModal.classList.remove('hidden');
                        }
                    });
                    chatbotBtn.setAttribute('data-listener', 'true');
                }

                if (mobileChatbotBtn && !mobileChatbotBtn.hasAttribute('data-listener')) {
                    mobileChatbotBtn.addEventListener('click', function() {
                        if (chatbotModal) {
                            chatbotModal.classList.remove('hidden');
                        }
                    });
                    mobileChatbotBtn.setAttribute('data-listener', 'true');
                }
            }, 500);
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile menu toggle


        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.addEventListener('DOMContentLoaded', () => {
            const animateElements = document.querySelectorAll('.card, .group, .bg-gray-50');
            animateElements.forEach(el => observer.observe(el));
        });

        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                    const icon = this.querySelector('i');
                    if (mobileMenu.classList.contains('hidden')) {
                        icon.className = 'fas fa-bars text-xl';
                    } else {
                        icon.className = 'fas fa-times text-xl';
                    }
                });

                // Close mobile menu when clicking on links
                mobileMenu.querySelectorAll('a').forEach(link => {
                    link.addEventListener('click', () => {
                        mobileMenu.classList.add('hidden');
                        mobileMenuBtn.querySelector('i').className = 'fas fa-bars text-xl';
                    });
                });
            }
        });

        // Add some interactive hover effects (only for non-touch devices)
        if (!('ontouchstart' in window)) {
            document.querySelectorAll('.group').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        }
    </script>
</body>
</html>
