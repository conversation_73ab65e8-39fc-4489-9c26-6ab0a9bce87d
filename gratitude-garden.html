<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gratitude Garden - MindSpace</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌱</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00b894 100%);
        }
        
        .garden-plot {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
            border-radius: 15px;
            border: 3px solid #654321;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .garden-plot:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .garden-plot.planted {
            background: linear-gradient(135deg, #2d5016 0%, #3e6b1f 100%);
        }
        
        .plant {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 2rem;
            animation: grow 1s ease-out;
        }
        
        @keyframes grow {
            0% { transform: translateX(-50%) scale(0); }
            100% { transform: translateX(-50%) scale(1); }
        }
        
        .floating-gratitude {
            position: absolute;
            color: white;
            font-weight: bold;
            pointer-events: none;
            animation: floatUp 3s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        @keyframes floatUp {
            0% { transform: translateY(0) scale(1); opacity: 1; }
            100% { transform: translateY(-80px) scale(1.2); opacity: 0; }
        }
        
        .gratitude-modal {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .seed-packet {
            background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
            border: 2px solid #e17055;
            transition: all 0.3s ease;
        }
        
        .seed-packet:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .garden-stats {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .celebration {
            animation: celebrate 2s ease-in-out;
        }
        
        @keyframes celebrate {
            0%, 100% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.1) rotate(5deg); }
            75% { transform: scale(1.1) rotate(-5deg); }
        }
    </style>
</head>
<body class="min-h-screen p-4">
    <!-- Back Button -->
    <button onclick="window.close()" class="absolute top-6 left-6 bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm">
        <i class="fas fa-arrow-left mr-2"></i>Back to Games
    </button>

    <div class="max-w-6xl mx-auto pt-20">
        <!-- Title -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">Gratitude Garden</h1>
            <p class="text-white/80">Plant seeds of gratitude and watch your garden bloom</p>
        </div>

        <!-- Stats -->
        <div class="flex justify-center space-x-6 mb-8">
            <div class="garden-stats rounded-lg px-6 py-3 text-center">
                <div class="text-2xl font-bold text-white" id="plantsCount">0</div>
                <div class="text-white/80 text-sm">Plants</div>
            </div>
            <div class="garden-stats rounded-lg px-6 py-3 text-center">
                <div class="text-2xl font-bold text-white" id="gratitudeCount">0</div>
                <div class="text-white/80 text-sm">Gratitudes</div>
            </div>
        </div>

        <!-- Seed Packets -->
        <div class="flex justify-center space-x-4 mb-8">
            <div class="seed-packet rounded-lg p-3 cursor-pointer" data-plant="🌱" data-name="Seedling">
                <div class="text-2xl text-center">🌱</div>
                <div class="text-xs text-center mt-1">Seedling</div>
            </div>
            <div class="seed-packet rounded-lg p-3 cursor-pointer" data-plant="🌸" data-name="Flower">
                <div class="text-2xl text-center">🌸</div>
                <div class="text-xs text-center mt-1">Flower</div>
            </div>
            <div class="seed-packet rounded-lg p-3 cursor-pointer" data-plant="🌻" data-name="Sunflower">
                <div class="text-2xl text-center">🌻</div>
                <div class="text-xs text-center mt-1">Sunflower</div>
            </div>
            <div class="seed-packet rounded-lg p-3 cursor-pointer" data-plant="🌺" data-name="Hibiscus">
                <div class="text-2xl text-center">🌺</div>
                <div class="text-xs text-center mt-1">Hibiscus</div>
            </div>
            <div class="seed-packet rounded-lg p-3 cursor-pointer" data-plant="🌷" data-name="Tulip">
                <div class="text-2xl text-center">🌷</div>
                <div class="text-xs text-center mt-1">Tulip</div>
            </div>
        </div>

        <!-- Garden Grid -->
        <div class="grid grid-cols-6 gap-4 max-w-3xl mx-auto mb-8" id="gardenGrid">
            <!-- Garden plots will be generated here -->
        </div>

        <!-- Instructions -->
        <div class="text-center text-white/80 text-sm">
            <p>Choose a seed, click an empty plot, and share what you're grateful for</p>
        </div>
    </div>

    <!-- Gratitude Modal -->
    <div id="gratitudeModal" class="fixed inset-0 bg-black/50 flex items-center justify-center hidden">
        <div class="gratitude-modal rounded-2xl p-8 max-w-md mx-4">
            <h3 class="text-2xl font-bold text-gray-800 mb-4 text-center">Plant Your Gratitude</h3>
            <p class="text-gray-600 mb-4 text-center">What are you grateful for today?</p>
            <textarea id="gratitudeText" class="w-full p-3 border border-gray-300 rounded-lg resize-none" rows="3" placeholder="I am grateful for..."></textarea>
            <div class="flex justify-center space-x-4 mt-6">
                <button id="plantBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-seedling mr-2"></i>Plant
                </button>
                <button id="cancelBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg transition-colors">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <script>
        let selectedSeed = '🌱';
        let selectedSeedName = 'Seedling';
        let selectedPlot = null;
        let plantsCount = 0;
        let gratitudeCount = 0;
        let gratitudes = [];

        // Initialize garden
        function initGarden() {
            const gardenGrid = document.getElementById('gardenGrid');
            
            // Create 24 garden plots (4x6 grid)
            for (let i = 0; i < 24; i++) {
                const plot = document.createElement('div');
                plot.className = 'garden-plot';
                plot.dataset.index = i;
                plot.addEventListener('click', () => selectPlot(plot));
                gardenGrid.appendChild(plot);
            }
        }

        // Select seed type
        function selectSeed(element) {
            document.querySelectorAll('.seed-packet').forEach(packet => {
                packet.classList.remove('ring-4', 'ring-white');
            });
            element.classList.add('ring-4', 'ring-white');
            
            selectedSeed = element.dataset.plant;
            selectedSeedName = element.dataset.name;
        }

        // Select plot to plant
        function selectPlot(plot) {
            if (plot.classList.contains('planted')) {
                // Show existing gratitude
                const index = plot.dataset.index;
                const gratitude = gratitudes[index];
                if (gratitude) {
                    showGratitude(gratitude);
                }
                return;
            }

            selectedPlot = plot;
            document.getElementById('gratitudeModal').classList.remove('hidden');
            document.getElementById('gratitudeText').focus();
        }

        // Plant the seed with gratitude
        function plantSeed() {
            const gratitudeText = document.getElementById('gratitudeText').value.trim();
            
            if (!gratitudeText) {
                alert('Please share what you\'re grateful for!');
                return;
            }

            if (!selectedPlot) return;

            // Plant the seed
            selectedPlot.classList.add('planted');
            
            const plant = document.createElement('div');
            plant.className = 'plant';
            plant.textContent = selectedSeed;
            selectedPlot.appendChild(plant);

            // Store gratitude
            const index = selectedPlot.dataset.index;
            gratitudes[index] = {
                text: gratitudeText,
                plant: selectedSeed,
                plantName: selectedSeedName,
                date: new Date().toLocaleDateString()
            };

            // Update stats
            plantsCount++;
            gratitudeCount++;
            updateStats();

            // Show floating gratitude
            showFloatingGratitude(selectedPlot, gratitudeText);

            // Close modal
            closeModal();

            // Celebration effect
            selectedPlot.classList.add('celebration');
            setTimeout(() => {
                selectedPlot.classList.remove('celebration');
            }, 2000);
        }

        // Show floating gratitude text
        function showFloatingGratitude(plot, text) {
            const rect = plot.getBoundingClientRect();
            const floating = document.createElement('div');
            floating.className = 'floating-gratitude';
            floating.textContent = text.substring(0, 20) + (text.length > 20 ? '...' : '');
            floating.style.left = rect.left + rect.width / 2 + 'px';
            floating.style.top = rect.top + 'px';
            floating.style.transform = 'translateX(-50%)';
            
            document.body.appendChild(floating);
            
            setTimeout(() => {
                document.body.removeChild(floating);
            }, 3000);
        }

        // Show gratitude details
        function showGratitude(gratitude) {
            alert(`${gratitude.plantName} planted on ${gratitude.date}\n\n"${gratitude.text}"`);
        }

        // Update statistics
        function updateStats() {
            document.getElementById('plantsCount').textContent = plantsCount;
            document.getElementById('gratitudeCount').textContent = gratitudeCount;
        }

        // Close modal
        function closeModal() {
            document.getElementById('gratitudeModal').classList.add('hidden');
            document.getElementById('gratitudeText').value = '';
            selectedPlot = null;
        }

        // Event listeners
        document.querySelectorAll('.seed-packet').forEach(packet => {
            packet.addEventListener('click', () => selectSeed(packet));
        });

        document.getElementById('plantBtn').addEventListener('click', plantSeed);
        document.getElementById('cancelBtn').addEventListener('click', closeModal);

        // Enter key to plant
        document.getElementById('gratitudeText').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                plantSeed();
            }
        });

        // Close modal on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // Initialize
        initGarden();
        selectSeed(document.querySelector('.seed-packet')); // Select first seed by default

        // Welcome message
        setTimeout(() => {
            const welcomeText = document.createElement('div');
            welcomeText.className = 'floating-gratitude';
            welcomeText.textContent = 'Welcome to your Gratitude Garden! 🌱';
            welcomeText.style.left = '50%';
            welcomeText.style.top = '50%';
            welcomeText.style.transform = 'translate(-50%, -50%)';
            welcomeText.style.fontSize = '1.5rem';
            
            document.body.appendChild(welcomeText);
            
            setTimeout(() => {
                document.body.removeChild(welcomeText);
            }, 3000);
        }, 1000);
    </script>
</body>
</html>
