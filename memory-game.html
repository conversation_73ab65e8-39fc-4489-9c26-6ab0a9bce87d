<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mindful Memory - MindSpace</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧠</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .memory-card {
            width: 120px;
            height: 120px;
            perspective: 1000px;
            cursor: pointer;
        }
        
        .card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }
        
        .memory-card.flipped .card-inner {
            transform: rotateY(180deg);
        }
        
        .card-front, .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .card-front {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #667eea;
        }
        
        .card-back {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            transform: rotateY(180deg);
            color: #8b5a3c;
        }
        
        .memory-card.matched .card-inner {
            transform: rotateY(180deg) scale(1.05);
            animation: matchPulse 0.6s ease-in-out;
        }
        
        @keyframes matchPulse {
            0%, 100% { transform: rotateY(180deg) scale(1.05); }
            50% { transform: rotateY(180deg) scale(1.15); }
        }
        
        .floating {
            animation: floating 6s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .stats-card {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .celebration {
            animation: celebrate 1s ease-in-out;
        }
        
        @keyframes celebrate {
            0%, 100% { transform: scale(1); }
            25% { transform: scale(1.1) rotate(5deg); }
            75% { transform: scale(1.1) rotate(-5deg); }
        }
    </style>
</head>
<body class="min-h-screen p-4">
    <!-- Back Button -->
    <button onclick="window.close()" class="absolute top-6 left-6 bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm">
        <i class="fas fa-arrow-left mr-2"></i>Back to Games
    </button>

    <!-- Floating Elements -->
    <div class="absolute top-20 right-10 w-16 h-16 bg-white/20 rounded-full floating"></div>
    <div class="absolute bottom-20 left-10 w-12 h-12 bg-white/20 rounded-full floating" style="animation-delay: 2s;"></div>

    <div class="max-w-4xl mx-auto pt-20">
        <!-- Title -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">Mindful Memory</h1>
            <p class="text-white/80">Enhance your focus through gentle memory exercises</p>
        </div>

        <!-- Stats -->
        <div class="flex justify-center space-x-6 mb-8">
            <div class="stats-card rounded-lg px-6 py-3 text-center">
                <div class="text-2xl font-bold text-white" id="moves">0</div>
                <div class="text-white/80 text-sm">Moves</div>
            </div>
            <div class="stats-card rounded-lg px-6 py-3 text-center">
                <div class="text-2xl font-bold text-white" id="matches">0</div>
                <div class="text-white/80 text-sm">Matches</div>
            </div>
            <div class="stats-card rounded-lg px-6 py-3 text-center">
                <div class="text-2xl font-bold text-white" id="timer">0:00</div>
                <div class="text-white/80 text-sm">Time</div>
            </div>
        </div>

        <!-- Game Board -->
        <div id="gameBoard" class="grid grid-cols-4 gap-4 max-w-2xl mx-auto mb-8">
            <!-- Cards will be generated here -->
        </div>

        <!-- Controls -->
        <div class="text-center">
            <button id="newGameBtn" class="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm font-medium">
                <i class="fas fa-refresh mr-2"></i>New Game
            </button>
        </div>

        <!-- Victory Message -->
        <div id="victoryMessage" class="fixed inset-0 bg-black/50 flex items-center justify-center hidden">
            <div class="bg-white rounded-2xl p-8 text-center max-w-md mx-4 celebration">
                <div class="text-6xl mb-4">🎉</div>
                <h2 class="text-3xl font-bold text-gray-800 mb-4">Congratulations!</h2>
                <p class="text-gray-600 mb-6">You've completed the memory challenge with mindful focus.</p>
                <div class="flex justify-center space-x-4">
                    <button onclick="newGame()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Play Again
                    </button>
                    <button onclick="closeVictory()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg transition-colors">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Game state
        let cards = [];
        let flippedCards = [];
        let moves = 0;
        let matches = 0;
        let gameStartTime = null;
        let timerInterval = null;
        let isProcessing = false;

        // Peaceful symbols for memory cards
        const symbols = ['🌸', '🍃', '🌙', '⭐', '🦋', '🌺', '🕊️', '🌿'];

        // Initialize game
        function initGame() {
            const gameBoard = document.getElementById('gameBoard');
            gameBoard.innerHTML = '';
            cards = [];
            flippedCards = [];
            moves = 0;
            matches = 0;
            gameStartTime = null;
            isProcessing = false;

            // Create card pairs
            const cardSymbols = [...symbols, ...symbols];
            cardSymbols.sort(() => Math.random() - 0.5);

            // Create card elements
            cardSymbols.forEach((symbol, index) => {
                const card = document.createElement('div');
                card.className = 'memory-card';
                card.dataset.symbol = symbol;
                card.dataset.index = index;

                card.innerHTML = `
                    <div class="card-inner">
                        <div class="card-front">
                            <i class="fas fa-question"></i>
                        </div>
                        <div class="card-back">
                            ${symbol}
                        </div>
                    </div>
                `;

                card.addEventListener('click', () => flipCard(card));
                gameBoard.appendChild(card);
                cards.push(card);
            });

            updateStats();
        }

        function flipCard(card) {
            if (isProcessing || card.classList.contains('flipped') || card.classList.contains('matched')) {
                return;
            }

            // Start timer on first move
            if (!gameStartTime) {
                gameStartTime = Date.now();
                timerInterval = setInterval(updateTimer, 1000);
            }

            card.classList.add('flipped');
            flippedCards.push(card);

            if (flippedCards.length === 2) {
                moves++;
                updateStats();
                checkMatch();
            }
        }

        function checkMatch() {
            isProcessing = true;
            const [card1, card2] = flippedCards;

            if (card1.dataset.symbol === card2.dataset.symbol) {
                // Match found
                setTimeout(() => {
                    card1.classList.add('matched');
                    card2.classList.add('matched');
                    matches++;
                    updateStats();
                    
                    flippedCards = [];
                    isProcessing = false;

                    // Check if game is complete
                    if (matches === symbols.length) {
                        setTimeout(showVictory, 500);
                    }
                }, 600);
            } else {
                // No match
                setTimeout(() => {
                    card1.classList.remove('flipped');
                    card2.classList.remove('flipped');
                    flippedCards = [];
                    isProcessing = false;
                }, 1000);
            }
        }

        function updateStats() {
            document.getElementById('moves').textContent = moves;
            document.getElementById('matches').textContent = matches;
        }

        function updateTimer() {
            if (!gameStartTime) return;
            
            const elapsed = Math.floor((Date.now() - gameStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('timer').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        function showVictory() {
            clearInterval(timerInterval);
            document.getElementById('victoryMessage').classList.remove('hidden');
        }

        function closeVictory() {
            document.getElementById('victoryMessage').classList.add('hidden');
        }

        function newGame() {
            clearInterval(timerInterval);
            closeVictory();
            initGame();
        }

        // Event listeners
        document.getElementById('newGameBtn').addEventListener('click', newGame);

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (e.code === 'KeyN') {
                newGame();
            }
        });

        // Initialize game on load
        initGame();

        // Add some visual flair
        setTimeout(() => {
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.animation = 'fadeIn 0.5s ease-in-out';
                }, index * 50);
            });
        }, 100);
    </script>
</body>
</html>
