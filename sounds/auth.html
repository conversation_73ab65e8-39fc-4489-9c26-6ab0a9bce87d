<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindSpace - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .font-display { font-family: 'Playfair Display', serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <!-- Floating Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full floating-animation"></div>
        <div class="absolute top-40 right-32 w-24 h-24 bg-purple-300/20 rounded-full floating-animation" style="animation-delay: 2s;"></div>
        <div class="absolute bottom-32 left-32 w-40 h-40 bg-indigo-300/15 rounded-full floating-animation" style="animation-delay: 4s;"></div>
        <div class="absolute bottom-20 right-20 w-28 h-28 bg-pink-300/20 rounded-full floating-animation" style="animation-delay: 1s;"></div>
    </div>

    <!-- Main Container -->
    <div class="w-full max-w-md">
        <!-- Logo and Title -->
        <div class="text-center mb-8 fade-in">
            <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 glass-effect">
                <i class="fas fa-om text-3xl text-white"></i>
            </div>
            <h1 class="text-4xl font-display font-bold text-white mb-2">MindSpace</h1>
            <p class="text-white/80 text-lg">Your journey to inner peace</p>
        </div>

        <!-- Auth Container -->
        <div class="glass-effect rounded-3xl p-8 shadow-2xl fade-in">
            <!-- Login Form -->
            <div id="loginForm" class="space-y-6">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2">Welcome Back</h2>
                    <p class="text-white/70">Sign in to continue your meditation journey</p>
                </div>

                <form id="loginFormElement" class="space-y-4">
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Email</label>
                        <input 
                            type="email" 
                            id="loginEmail" 
                            required
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                            placeholder="Enter your email"
                        >
                    </div>
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Password</label>
                        <input 
                            type="password" 
                            id="loginPassword" 
                            required
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                            placeholder="Enter your password"
                        >
                    </div>
                    <button 
                        type="submit" 
                        class="w-full bg-white text-indigo-600 py-3 rounded-xl font-semibold hover:bg-white/90 transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                        Sign In
                    </button>
                </form>

                <div class="text-center">
                    <p class="text-white/70">Don't have an account? 
                        <button onclick="showSignup()" class="text-white font-semibold hover:underline">Sign up</button>
                    </p>
                </div>

                <!-- Demo Accounts -->
                <div class="border-t border-white/20 pt-4">
                    <p class="text-white/70 text-sm text-center mb-3">Demo Accounts:</p>
                    <div class="space-y-2">
                        <button onclick="loginDemo('<EMAIL>', 'demo123')" class="w-full text-left bg-white/5 hover:bg-white/10 p-3 rounded-lg transition-colors">
                            <div class="text-white text-sm font-medium">Demo User</div>
                            <div class="text-white/60 text-xs"><EMAIL> / demo123</div>
                        </button>
                        <button onclick="loginDemo('<EMAIL>', 'admin123')" class="w-full text-left bg-white/5 hover:bg-white/10 p-3 rounded-lg transition-colors">
                            <div class="text-white text-sm font-medium">Admin User</div>
                            <div class="text-white/60 text-xs"><EMAIL> / admin123</div>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Signup Form -->
            <div id="signupForm" class="space-y-6 hidden">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2">Create Account</h2>
                    <p class="text-white/70">Join MindSpace and start your wellness journey</p>
                </div>

                <form id="signupFormElement" class="space-y-4">
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Full Name</label>
                        <input 
                            type="text" 
                            id="signupName" 
                            required
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                            placeholder="Enter your full name"
                        >
                    </div>
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Email</label>
                        <input 
                            type="email" 
                            id="signupEmail" 
                            required
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                            placeholder="Enter your email"
                        >
                    </div>
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Password</label>
                        <input 
                            type="password" 
                            id="signupPassword" 
                            required
                            minlength="6"
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                            placeholder="Create a password (min 6 characters)"
                        >
                    </div>
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Confirm Password</label>
                        <input 
                            type="password" 
                            id="signupConfirmPassword" 
                            required
                            class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                            placeholder="Confirm your password"
                        >
                    </div>
                    <button 
                        type="submit" 
                        class="w-full bg-white text-indigo-600 py-3 rounded-xl font-semibold hover:bg-white/90 transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                        Create Account
                    </button>
                </form>

                <div class="text-center">
                    <p class="text-white/70">Already have an account? 
                        <button onclick="showLogin()" class="text-white font-semibold hover:underline">Sign in</button>
                    </p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-white/60 text-sm">
            <p>&copy; 2024 MindSpace. Your wellness journey starts here.</p>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

    <script>
        // Initialize demo accounts and auth system
        function initializeAuth() {
            // Create demo accounts if they don't exist
            const users = JSON.parse(localStorage.getItem('mindspace_users') || '{}');
            
            if (!users['<EMAIL>']) {
                users['<EMAIL>'] = {
                    name: 'Demo User',
                    email: '<EMAIL>',
                    password: 'demo123',
                    createdAt: new Date().toISOString()
                };
            }
            
            if (!users['<EMAIL>']) {
                users['<EMAIL>'] = {
                    name: 'Admin User',
                    email: '<EMAIL>',
                    password: 'admin123',
                    createdAt: new Date().toISOString()
                };
            }
            
            localStorage.setItem('mindspace_users', JSON.stringify(users));
        }

        // Show/Hide forms
        function showSignup() {
            document.getElementById('loginForm').classList.add('hidden');
            document.getElementById('signupForm').classList.remove('hidden');
        }

        function showLogin() {
            document.getElementById('signupForm').classList.add('hidden');
            document.getElementById('loginForm').classList.remove('hidden');
        }

        // Demo login function
        function loginDemo(email, password) {
            document.getElementById('loginEmail').value = email;
            document.getElementById('loginPassword').value = password;
            handleLogin(email, password);
        }

        // Show message function
        function showMessage(message, type = 'success') {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `p-4 rounded-lg shadow-lg mb-4 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white transform translate-x-full transition-transform duration-300`;
            messageDiv.textContent = message;
            
            container.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.classList.remove('translate-x-full');
            }, 100);
            
            setTimeout(() => {
                messageDiv.classList.add('translate-x-full');
                setTimeout(() => {
                    container.removeChild(messageDiv);
                }, 300);
            }, 3000);
        }

        // Handle login
        function handleLogin(email, password) {
            const users = JSON.parse(localStorage.getItem('mindspace_users') || '{}');
            
            if (users[email] && users[email].password === password) {
                // Store current user session
                localStorage.setItem('mindspace_current_user', JSON.stringify(users[email]));
                showMessage('Login successful! Redirecting...', 'success');
                
                setTimeout(() => {
                    window.location.href = 'main.html';
                }, 1500);
            } else {
                showMessage('Invalid email or password', 'error');
            }
        }

        // Handle signup
        function handleSignup(name, email, password, confirmPassword) {
            if (password !== confirmPassword) {
                showMessage('Passwords do not match', 'error');
                return;
            }
            
            const users = JSON.parse(localStorage.getItem('mindspace_users') || '{}');
            
            if (users[email]) {
                showMessage('Email already exists', 'error');
                return;
            }
            
            users[email] = {
                name: name,
                email: email,
                password: password,
                createdAt: new Date().toISOString()
            };
            
            localStorage.setItem('mindspace_users', JSON.stringify(users));
            localStorage.setItem('mindspace_current_user', JSON.stringify(users[email]));
            
            showMessage('Account created successfully! Redirecting...', 'success');
            
            setTimeout(() => {
                window.location.href = 'main.html';
            }, 1500);
        }

        // Event listeners
        document.getElementById('loginFormElement').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            handleLogin(email, password);
        });

        document.getElementById('signupFormElement').addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('signupName').value;
            const email = document.getElementById('signupEmail').value;
            const password = document.getElementById('signupPassword').value;
            const confirmPassword = document.getElementById('signupConfirmPassword').value;
            handleSignup(name, email, password, confirmPassword);
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeAuth();
            
            // Check if user is already logged in
            const currentUser = localStorage.getItem('mindspace_current_user');
            if (currentUser) {
                window.location.href = 'main.html';
            }
        });
    </script>
</body>
</html>
