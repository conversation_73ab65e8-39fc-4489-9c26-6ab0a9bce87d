<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Breathing Harmony - MindSpace</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🫁</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }
        
        .breathing-circle {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 70%, transparent 100%);
            border: 3px solid rgba(255,255,255,0.5);
            position: relative;
            transition: all 4s ease-in-out;
        }
        
        .breathing-circle.inhale {
            transform: scale(1.5);
            background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.2) 70%, transparent 100%);
        }
        
        .breathing-circle.exhale {
            transform: scale(0.8);
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 70%, transparent 100%);
        }
        
        .inner-circle {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 4s ease-in-out;
        }
        
        .breathing-circle.inhale .inner-circle {
            transform: translate(-50%, -50%) scale(1.3);
            background: rgba(255,255,255,0.3);
        }
        
        .breathing-circle.exhale .inner-circle {
            transform: translate(-50%, -50%) scale(0.7);
            background: rgba(255,255,255,0.15);
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            border: 2px solid rgba(255,255,255,0.3);
            animation: ripple 4s ease-out infinite;
        }
        
        @keyframes ripple {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
            }
            100% {
                width: 600px;
                height: 600px;
                opacity: 0;
            }
        }
        
        .floating-particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: rgba(255,255,255,0.6);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            50% { transform: translateY(-100px) rotate(180deg); }
        }
        
        .instruction-text {
            transition: all 1s ease-in-out;
            opacity: 0.9;
        }
        
        .pulse {
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center relative">
    <!-- Floating Particles -->
    <div class="floating-particle" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
    <div class="floating-particle" style="top: 30%; right: 15%; animation-delay: 1s;"></div>
    <div class="floating-particle" style="bottom: 25%; left: 20%; animation-delay: 2s;"></div>
    <div class="floating-particle" style="bottom: 35%; right: 25%; animation-delay: 3s;"></div>
    <div class="floating-particle" style="top: 50%; left: 5%; animation-delay: 4s;"></div>
    <div class="floating-particle" style="top: 60%; right: 8%; animation-delay: 5s;"></div>

    <!-- Back Button -->
    <button onclick="window.close()" class="absolute top-6 left-6 bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm">
        <i class="fas fa-arrow-left mr-2"></i>Back to Games
    </button>

    <!-- Main Content -->
    <div class="text-center text-white">
        <!-- Title -->
        <h1 class="text-4xl font-bold mb-4">Breathing Harmony</h1>
        <p class="text-lg mb-8 opacity-90">Follow the circle and breathe deeply</p>
        
        <!-- Breathing Circle Container -->
        <div class="relative flex items-center justify-center mb-8">
            <div id="breathingCircle" class="breathing-circle">
                <div class="inner-circle"></div>
                <!-- Ripple effects will be added here -->
            </div>
        </div>
        
        <!-- Instructions -->
        <div id="instructionText" class="instruction-text text-2xl font-medium mb-8">
            Click to start your breathing journey
        </div>
        
        <!-- Controls -->
        <div class="flex justify-center space-x-4 mb-6">
            <button id="startBtn" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm">
                <i class="fas fa-play mr-2"></i>Start
            </button>
            <button id="pauseBtn" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm" style="display: none;">
                <i class="fas fa-pause mr-2"></i>Pause
            </button>
            <button id="resetBtn" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm">
                <i class="fas fa-refresh mr-2"></i>Reset
            </button>
        </div>
        
        <!-- Stats -->
        <div class="flex justify-center space-x-8 text-sm opacity-80">
            <div>
                <div class="text-lg font-bold" id="breathCount">0</div>
                <div>Breaths</div>
            </div>
            <div>
                <div class="text-lg font-bold" id="sessionTime">0:00</div>
                <div>Time</div>
            </div>
        </div>
    </div>

    <script>
        let isBreathing = false;
        let breathCount = 0;
        let sessionStartTime = null;
        let breathingInterval = null;
        let timeInterval = null;
        let currentPhase = 'ready'; // ready, inhale, hold, exhale, hold2
        
        const circle = document.getElementById('breathingCircle');
        const instructionText = document.getElementById('instructionText');
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const resetBtn = document.getElementById('resetBtn');
        const breathCountEl = document.getElementById('breathCount');
        const sessionTimeEl = document.getElementById('sessionTime');
        
        // Breathing cycle timing (in milliseconds)
        const phases = {
            inhale: 4000,
            hold1: 1000,
            exhale: 6000,
            hold2: 1000
        };
        
        function startBreathing() {
            if (isBreathing) return;
            
            isBreathing = true;
            sessionStartTime = Date.now();
            startBtn.style.display = 'none';
            pauseBtn.style.display = 'inline-block';
            
            // Start time counter
            timeInterval = setInterval(updateTime, 1000);
            
            // Start breathing cycle
            breathingCycle();
        }
        
        function pauseBreathing() {
            isBreathing = false;
            clearTimeout(breathingInterval);
            clearInterval(timeInterval);
            startBtn.style.display = 'inline-block';
            pauseBtn.style.display = 'none';
            instructionText.textContent = 'Paused - Click Start to continue';
            circle.className = 'breathing-circle';
        }
        
        function resetBreathing() {
            isBreathing = false;
            breathCount = 0;
            sessionStartTime = null;
            currentPhase = 'ready';
            
            clearTimeout(breathingInterval);
            clearInterval(timeInterval);
            
            startBtn.style.display = 'inline-block';
            pauseBtn.style.display = 'none';
            
            circle.className = 'breathing-circle';
            instructionText.textContent = 'Click to start your breathing journey';
            breathCountEl.textContent = '0';
            sessionTimeEl.textContent = '0:00';
            
            // Remove all ripples
            document.querySelectorAll('.ripple').forEach(ripple => ripple.remove());
        }
        
        function breathingCycle() {
            if (!isBreathing) return;
            
            // Inhale phase
            currentPhase = 'inhale';
            circle.className = 'breathing-circle inhale';
            instructionText.textContent = 'Breathe In...';
            createRipple();
            
            breathingInterval = setTimeout(() => {
                if (!isBreathing) return;
                
                // Hold phase 1
                currentPhase = 'hold1';
                instructionText.textContent = 'Hold...';
                
                setTimeout(() => {
                    if (!isBreathing) return;
                    
                    // Exhale phase
                    currentPhase = 'exhale';
                    circle.className = 'breathing-circle exhale';
                    instructionText.textContent = 'Breathe Out...';
                    
                    setTimeout(() => {
                        if (!isBreathing) return;
                        
                        // Hold phase 2
                        currentPhase = 'hold2';
                        instructionText.textContent = 'Hold...';
                        
                        setTimeout(() => {
                            if (!isBreathing) return;
                            
                            // Complete one breath cycle
                            breathCount++;
                            breathCountEl.textContent = breathCount;
                            
                            // Start next cycle
                            breathingCycle();
                        }, phases.hold2);
                    }, phases.exhale);
                }, phases.hold1);
            }, phases.inhale);
        }
        
        function createRipple() {
            const ripple = document.createElement('div');
            ripple.className = 'ripple';
            ripple.style.top = '50%';
            ripple.style.left = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';
            
            circle.appendChild(ripple);
            
            // Remove ripple after animation
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 4000);
        }
        
        function updateTime() {
            if (!sessionStartTime) return;
            
            const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            sessionTimeEl.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // Event listeners
        startBtn.addEventListener('click', startBreathing);
        pauseBtn.addEventListener('click', pauseBreathing);
        resetBtn.addEventListener('click', resetBreathing);
        
        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                if (isBreathing) {
                    pauseBreathing();
                } else {
                    startBreathing();
                }
            } else if (e.code === 'KeyR') {
                resetBreathing();
            }
        });
    </script>
</body>
</html>
