<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zen Garden - MindSpace</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏯</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            overflow: hidden;
        }
        
        #zenCanvas {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            cursor: crosshair;
            background: linear-gradient(45deg, #f4f1de 0%, #e9c46a 100%);
        }
        
        .tool-button {
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
        }
        
        .tool-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .tool-button.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
        }
        
        .floating {
            animation: floating 6s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .zen-quote {
            animation: fadeInOut 8s ease-in-out infinite;
        }
        
        @keyframes fadeInOut {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <!-- Back Button -->
    <button onclick="window.close()" class="absolute top-6 left-6 bg-white/80 hover:bg-white text-gray-700 px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm shadow-lg">
        <i class="fas fa-arrow-left mr-2"></i>Back to Games
    </button>

    <!-- Zen Quote -->
    <div class="absolute top-6 right-6 zen-quote text-gray-600 text-sm max-w-xs text-right">
        "The mind is everything. What you think you become." - Buddha
    </div>

    <div class="max-w-6xl w-full">
        <!-- Title -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">Zen Garden</h1>
            <p class="text-gray-600">Create beautiful patterns in the sand and find your inner peace</p>
        </div>

        <!-- Canvas Container -->
        <div class="relative mb-6">
            <canvas id="zenCanvas" width="800" height="500" class="mx-auto block"></canvas>
            
            <!-- Floating Elements -->
            <div class="absolute -top-4 -left-4 w-8 h-8 bg-yellow-300 rounded-full floating opacity-60"></div>
            <div class="absolute -top-4 -right-4 w-6 h-6 bg-blue-300 rounded-full floating opacity-60" style="animation-delay: 2s;"></div>
            <div class="absolute -bottom-4 -left-4 w-10 h-10 bg-green-300 rounded-full floating opacity-60" style="animation-delay: 4s;"></div>
            <div class="absolute -bottom-4 -right-4 w-7 h-7 bg-purple-300 rounded-full floating opacity-60" style="animation-delay: 6s;"></div>
        </div>

        <!-- Tools -->
        <div class="flex justify-center space-x-4 mb-6">
            <button id="rakeBtn" class="tool-button active px-6 py-3 rounded-lg font-medium">
                <i class="fas fa-grip-lines mr-2"></i>Rake
            </button>
            <button id="stoneBtn" class="tool-button px-6 py-3 rounded-lg font-medium">
                <i class="fas fa-circle mr-2"></i>Stone
            </button>
            <button id="waveBtn" class="tool-button px-6 py-3 rounded-lg font-medium">
                <i class="fas fa-water mr-2"></i>Wave
            </button>
            <button id="spiralBtn" class="tool-button px-6 py-3 rounded-lg font-medium">
                <i class="fas fa-hurricane mr-2"></i>Spiral
            </button>
            <button id="clearBtn" class="tool-button px-6 py-3 rounded-lg font-medium">
                <i class="fas fa-eraser mr-2"></i>Clear
            </button>
        </div>

        <!-- Instructions -->
        <div class="text-center text-gray-600 text-sm">
            <p>Click and drag to create patterns • Use different tools for various effects • Let your creativity flow</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('zenCanvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let currentTool = 'rake';
        let lastX = 0;
        let lastY = 0;
        
        // Tool buttons
        const rakeBtn = document.getElementById('rakeBtn');
        const stoneBtn = document.getElementById('stoneBtn');
        const waveBtn = document.getElementById('waveBtn');
        const spiralBtn = document.getElementById('spiralBtn');
        const clearBtn = document.getElementById('clearBtn');
        
        // Initialize canvas
        function initCanvas() {
            ctx.fillStyle = '#f4f1de';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Add subtle texture
            for (let i = 0; i < 1000; i++) {
                ctx.fillStyle = `rgba(233, 196, 106, ${Math.random() * 0.1})`;
                ctx.fillRect(Math.random() * canvas.width, Math.random() * canvas.height, 2, 2);
            }
        }
        
        // Tool functions
        function setTool(tool) {
            currentTool = tool;
            document.querySelectorAll('.tool-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tool + 'Btn').classList.add('active');
        }
        
        function drawRake(x, y) {
            ctx.strokeStyle = '#d4a574';
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';
            
            ctx.beginPath();
            ctx.moveTo(lastX, lastY);
            ctx.lineTo(x, y);
            ctx.stroke();
            
            // Add rake lines
            const angle = Math.atan2(y - lastY, x - lastX);
            for (let i = -2; i <= 2; i++) {
                const offsetX = Math.cos(angle + Math.PI/2) * i * 8;
                const offsetY = Math.sin(angle + Math.PI/2) * i * 8;
                
                ctx.beginPath();
                ctx.moveTo(lastX + offsetX, lastY + offsetY);
                ctx.lineTo(x + offsetX, y + offsetY);
                ctx.stroke();
            }
        }
        
        function drawStone(x, y) {
            const radius = 20 + Math.random() * 20;
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
            gradient.addColorStop(0, '#8d7053');
            gradient.addColorStop(1, '#5d4e37');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
            
            // Add highlight
            ctx.fillStyle = 'rgba(255,255,255,0.3)';
            ctx.beginPath();
            ctx.arc(x - radius/3, y - radius/3, radius/3, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawWave(x, y) {
            ctx.strokeStyle = '#b8a082';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            
            const amplitude = 20;
            const frequency = 0.02;
            
            ctx.beginPath();
            for (let i = 0; i < 100; i++) {
                const waveX = lastX + (x - lastX) * (i / 100);
                const waveY = lastY + (y - lastY) * (i / 100) + Math.sin(i * frequency) * amplitude;
                
                if (i === 0) {
                    ctx.moveTo(waveX, waveY);
                } else {
                    ctx.lineTo(waveX, waveY);
                }
            }
            ctx.stroke();
        }
        
        function drawSpiral(x, y) {
            ctx.strokeStyle = '#c9b037';
            ctx.lineWidth = 2;
            ctx.lineCap = 'round';
            
            const centerX = x;
            const centerY = y;
            const maxRadius = 50;
            
            ctx.beginPath();
            for (let angle = 0; angle < Math.PI * 6; angle += 0.1) {
                const radius = (angle / (Math.PI * 6)) * maxRadius;
                const spiralX = centerX + Math.cos(angle) * radius;
                const spiralY = centerY + Math.sin(angle) * radius;
                
                if (angle === 0) {
                    ctx.moveTo(spiralX, spiralY);
                } else {
                    ctx.lineTo(spiralX, spiralY);
                }
            }
            ctx.stroke();
        }
        
        function clearCanvas() {
            initCanvas();
        }
        
        // Event listeners
        canvas.addEventListener('mousedown', (e) => {
            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            lastX = e.clientX - rect.left;
            lastY = e.clientY - rect.top;
            
            if (currentTool === 'stone') {
                drawStone(lastX, lastY);
            } else if (currentTool === 'spiral') {
                drawSpiral(lastX, lastY);
            }
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (!isDrawing) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            if (currentTool === 'rake') {
                drawRake(x, y);
            } else if (currentTool === 'wave') {
                drawWave(x, y);
            }
            
            lastX = x;
            lastY = y;
        });
        
        canvas.addEventListener('mouseup', () => {
            isDrawing = false;
        });
        
        canvas.addEventListener('mouseout', () => {
            isDrawing = false;
        });
        
        // Touch events for mobile
        canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            const rect = canvas.getBoundingClientRect();
            lastX = touch.clientX - rect.left;
            lastY = touch.clientY - rect.top;
            isDrawing = true;
            
            if (currentTool === 'stone') {
                drawStone(lastX, lastY);
            } else if (currentTool === 'spiral') {
                drawSpiral(lastX, lastY);
            }
        });
        
        canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            if (!isDrawing) return;
            
            const touch = e.touches[0];
            const rect = canvas.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;
            
            if (currentTool === 'rake') {
                drawRake(x, y);
            } else if (currentTool === 'wave') {
                drawWave(x, y);
            }
            
            lastX = x;
            lastY = y;
        });
        
        canvas.addEventListener('touchend', () => {
            isDrawing = false;
        });
        
        // Tool button events
        rakeBtn.addEventListener('click', () => setTool('rake'));
        stoneBtn.addEventListener('click', () => setTool('stone'));
        waveBtn.addEventListener('click', () => setTool('wave'));
        spiralBtn.addEventListener('click', () => setTool('spiral'));
        clearBtn.addEventListener('click', clearCanvas);
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1': setTool('rake'); break;
                case '2': setTool('stone'); break;
                case '3': setTool('wave'); break;
                case '4': setTool('spiral'); break;
                case 'c': case 'C': clearCanvas(); break;
            }
        });
        
        // Initialize
        initCanvas();
        
        // Zen quotes rotation
        const zenQuotes = [
            "The mind is everything. What you think you become. - Buddha",
            "Peace comes from within. Do not seek it without. - Buddha",
            "In the midst of movement and chaos, keep stillness inside of you. - Deepak Chopra",
            "The present moment is the only time over which we have dominion. - Thích Nhất Hạnh",
            "Wherever you are, be there totally. - Eckhart Tolle"
        ];
        
        let quoteIndex = 0;
        setInterval(() => {
            quoteIndex = (quoteIndex + 1) % zenQuotes.length;
            document.querySelector('.zen-quote').textContent = zenQuotes[quoteIndex];
        }, 8000);
    </script>
</body>
</html>
