<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Flow - MindSpace</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎨</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #000;
            overflow: hidden;
            cursor: none;
        }
        
        #colorCanvas {
            display: block;
            background: radial-gradient(circle, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
        }
        
        .control-panel {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .color-orb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .color-orb:hover {
            transform: scale(1.2);
            box-shadow: 0 0 20px currentColor;
        }
        
        .color-orb.active {
            transform: scale(1.3);
            box-shadow: 0 0 30px currentColor;
            border-color: white;
        }
        
        .floating-instruction {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); opacity: 0.7; }
            50% { transform: translateY(-10px); opacity: 1; }
        }
        
        .pulse-button {
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
    </style>
</head>
<body class="min-h-screen flex flex-col">
    <!-- Back Button -->
    <button onclick="window.close()" class="absolute top-6 left-6 z-10 bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/20">
        <i class="fas fa-arrow-left mr-2"></i>Back to Games
    </button>

    <!-- Instructions -->
    <div class="absolute top-6 right-6 z-10 text-white text-right floating-instruction">
        <div class="text-lg font-medium mb-1">Color Flow Meditation</div>
        <div class="text-sm opacity-80">Move your mouse to paint with light</div>
        <div class="text-xs opacity-60 mt-1">Press SPACE to clear • Click colors to change</div>
    </div>

    <!-- Canvas -->
    <canvas id="colorCanvas" class="flex-1"></canvas>

    <!-- Control Panel -->
    <div class="control-panel fixed bottom-6 left-1/2 transform -translate-x-1/2 px-6 py-4 rounded-2xl">
        <div class="flex items-center space-x-4">
            <!-- Color Palette -->
            <div class="flex space-x-2">
                <div class="color-orb active" style="background: #ff6b6b;" data-color="#ff6b6b"></div>
                <div class="color-orb" style="background: #4ecdc4;" data-color="#4ecdc4"></div>
                <div class="color-orb" style="background: #45b7d1;" data-color="#45b7d1"></div>
                <div class="color-orb" style="background: #96ceb4;" data-color="#96ceb4"></div>
                <div class="color-orb" style="background: #feca57;" data-color="#feca57"></div>
                <div class="color-orb" style="background: #ff9ff3;" data-color="#ff9ff3"></div>
                <div class="color-orb" style="background: #a8e6cf;" data-color="#a8e6cf"></div>
                <div class="color-orb" style="background: #dda0dd;" data-color="#dda0dd"></div>
            </div>
            
            <!-- Divider -->
            <div class="w-px h-8 bg-white/20"></div>
            
            <!-- Controls -->
            <button id="clearBtn" class="text-white hover:text-gray-300 transition-colors pulse-button">
                <i class="fas fa-eraser text-lg"></i>
            </button>
            
            <button id="autoModeBtn" class="text-white hover:text-gray-300 transition-colors">
                <i class="fas fa-magic text-lg"></i>
            </button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('colorCanvas');
        const ctx = canvas.getContext('2d');
        let currentColor = '#ff6b6b';
        let isAutoMode = false;
        let particles = [];
        let mouseX = 0;
        let mouseY = 0;
        let isMouseMoving = false;
        
        // Resize canvas to full screen
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        
        // Particle class for flowing colors
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = (Math.random() - 0.5) * 2;
                this.life = 1.0;
                this.decay = Math.random() * 0.02 + 0.005;
                this.size = Math.random() * 30 + 10;
                this.color = color;
                this.hue = this.extractHue(color);
            }
            
            extractHue(color) {
                // Simple hue extraction from hex color
                const r = parseInt(color.substr(1, 2), 16) / 255;
                const g = parseInt(color.substr(3, 2), 16) / 255;
                const b = parseInt(color.substr(5, 2), 16) / 255;
                
                const max = Math.max(r, g, b);
                const min = Math.min(r, g, b);
                let h = 0;
                
                if (max !== min) {
                    const d = max - min;
                    switch (max) {
                        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                        case g: h = (b - r) / d + 2; break;
                        case b: h = (r - g) / d + 4; break;
                    }
                    h /= 6;
                }
                
                return h * 360;
            }
            
            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.vx *= 0.99;
                this.vy *= 0.99;
                
                // Add some drift
                this.vx += (Math.random() - 0.5) * 0.1;
                this.vy += (Math.random() - 0.5) * 0.1;
            }
            
            draw() {
                const alpha = this.life;
                const size = this.size * this.life;
                
                // Create gradient
                const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, size);
                gradient.addColorStop(0, `hsla(${this.hue}, 70%, 60%, ${alpha})`);
                gradient.addColorStop(0.5, `hsla(${this.hue + 30}, 80%, 70%, ${alpha * 0.5})`);
                gradient.addColorStop(1, `hsla(${this.hue + 60}, 90%, 80%, 0)`);
                
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(this.x, this.y, size, 0, Math.PI * 2);
                ctx.fill();
            }
            
            isDead() {
                return this.life <= 0;
            }
        }
        
        // Create particles at mouse position
        function createParticles(x, y) {
            for (let i = 0; i < 3; i++) {
                particles.push(new Particle(
                    x + (Math.random() - 0.5) * 20,
                    y + (Math.random() - 0.5) * 20,
                    currentColor
                ));
            }
        }
        
        // Auto mode - creates particles automatically
        function autoModeParticles() {
            if (!isAutoMode) return;
            
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#a8e6cf', '#dda0dd'];
            const color = colors[Math.floor(Math.random() * colors.length)];
            
            for (let i = 0; i < 2; i++) {
                particles.push(new Particle(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    color
                ));
            }
        }
        
        // Animation loop
        function animate() {
            // Fade effect instead of clearing
            ctx.fillStyle = 'rgba(26, 26, 46, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw particles
            for (let i = particles.length - 1; i >= 0; i--) {
                const particle = particles[i];
                particle.update();
                particle.draw();
                
                if (particle.isDead()) {
                    particles.splice(i, 1);
                }
            }
            
            // Create particles if mouse is moving
            if (isMouseMoving) {
                createParticles(mouseX, mouseY);
                isMouseMoving = false;
            }
            
            // Auto mode
            if (Math.random() < 0.1) {
                autoModeParticles();
            }
            
            requestAnimationFrame(animate);
        }
        
        // Event listeners
        canvas.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
            isMouseMoving = true;
        });
        
        canvas.addEventListener('click', (e) => {
            // Create burst of particles on click
            for (let i = 0; i < 10; i++) {
                createParticles(e.clientX, e.clientY);
            }
        });
        
        // Color selection
        document.querySelectorAll('.color-orb').forEach(orb => {
            orb.addEventListener('click', () => {
                document.querySelectorAll('.color-orb').forEach(o => o.classList.remove('active'));
                orb.classList.add('active');
                currentColor = orb.dataset.color;
            });
        });
        
        // Clear button
        document.getElementById('clearBtn').addEventListener('click', () => {
            particles = [];
            ctx.fillStyle = 'rgba(26, 26, 46, 1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        });
        
        // Auto mode toggle
        document.getElementById('autoModeBtn').addEventListener('click', () => {
            isAutoMode = !isAutoMode;
            const btn = document.getElementById('autoModeBtn');
            if (isAutoMode) {
                btn.style.color = '#feca57';
                btn.innerHTML = '<i class="fas fa-magic text-lg"></i>';
            } else {
                btn.style.color = 'white';
                btn.innerHTML = '<i class="fas fa-magic text-lg"></i>';
            }
        });
        
        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                particles = [];
                ctx.fillStyle = 'rgba(26, 26, 46, 1)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
            } else if (e.code === 'KeyA') {
                document.getElementById('autoModeBtn').click();
            }
        });
        
        // Window resize
        window.addEventListener('resize', resizeCanvas);
        
        // Initialize
        resizeCanvas();
        
        // Initial background
        ctx.fillStyle = 'rgba(26, 26, 46, 1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Start animation
        animate();
        
        // Welcome particles
        setTimeout(() => {
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    particles.push(new Particle(
                        Math.random() * canvas.width,
                        Math.random() * canvas.height,
                        ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'][Math.floor(Math.random() * 4)]
                    ));
                }, i * 100);
            }
        }, 500);
    </script>
</body>
</html>
