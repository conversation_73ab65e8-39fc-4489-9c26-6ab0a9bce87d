<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mindful Games - MindSpace</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎮</text></svg>">
    <meta name="description" content="Peaceful and mind-motivating games for relaxation and mental wellness.">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .font-display {
            font-family: 'Playfair Display', serif;
        }
        
        /* Watery Button Animations */
        .water-button {
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .water-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }
        
        .water-button:hover::before {
            left: 100%;
        }
        
        .water-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        /* Ripple Effect */
        .ripple {
            position: relative;
            overflow: hidden;
        }
        
        .ripple::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }
        
        .ripple:active::after {
            width: 300px;
            height: 300px;
        }
        
        /* Floating Animation */
        .floating {
            animation: floating 6s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        /* Water Wave Animation */
        .wave {
            position: relative;
            overflow: hidden;
        }
        
        .wave::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 200%;
            height: 100%;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: wave 3s linear infinite;
        }
        
        @keyframes wave {
            0% { transform: translateX(-50%); }
            100% { transform: translateX(0%); }
        }
        
        /* Game Card Hover Effects */
        .game-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        
        .game-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        /* Breathing Circle Animation */
        .breathing-circle {
            animation: breathe 4s ease-in-out infinite;
        }
        
        @keyframes breathe {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }
        
        /* Zen Garden Rake Animation */
        .rake-line {
            stroke-dasharray: 200;
            stroke-dashoffset: 200;
            animation: draw 3s ease-in-out infinite;
        }
        
        @keyframes draw {
            0% { stroke-dashoffset: 200; }
            50% { stroke-dashoffset: 0; }
            100% { stroke-dashoffset: -200; }
        }

        /* Enhanced Breathing Circle Styles */
        .breathing-circle {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 70%, transparent 100%);
            border: 3px solid rgba(255,255,255,0.5);
            position: relative;
            transition: all 4s ease-in-out;
        }

        .breathing-circle.inhale {
            transform: scale(1.5);
            background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.2) 70%, transparent 100%);
        }

        .breathing-circle.exhale {
            transform: scale(0.8);
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 70%, transparent 100%);
        }

        .inner-circle {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: all 4s ease-in-out;
        }

        .breathing-circle.inhale .inner-circle {
            transform: translate(-50%, -50%) scale(1.3);
            background: rgba(255,255,255,0.3);
        }

        .breathing-circle.exhale .inner-circle {
            transform: translate(-50%, -50%) scale(0.7);
            background: rgba(255,255,255,0.15);
        }

        /* Floating particles */
        .floating-particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: rgba(255,255,255,0.6);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            50% { transform: translateY(-100px) rotate(180deg); }
        }

        /* Memory card styles */
        .memory-card {
            perspective: 1000px;
        }

        .card-inner {
            transform-style: preserve-3d;
            position: relative;
            width: 100%;
            height: 100%;
            transition: transform 0.6s;
        }

        .memory-card.flipped .card-inner {
            transform: rotateY(180deg) !important;
        }

        .card-front, .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .card-back {
            transform: rotateY(180deg);
        }

        .memory-card.matched .card-inner {
            transform: rotateY(180deg) scale(1.05);
            animation: matchPulse 0.6s ease-in-out;
        }

        @keyframes matchPulse {
            0%, 100% { transform: rotateY(180deg) scale(1.05); }
            50% { transform: rotateY(180deg) scale(1.15); }
        }

        /* Garden plot styles */
        .garden-plot {
            position: relative;
            overflow: hidden;
        }

        .garden-plot.planted {
            background: linear-gradient(135deg, #2d5016 0%, #3e6b1f 100%) !important;
        }

        @keyframes grow {
            0% { transform: translateX(-50%) scale(0); }
            100% { transform: translateX(-50%) scale(1); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-white/90 backdrop-blur-md border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold gradient-bg bg-clip-text text-transparent">MindSpace</h1>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="main.html" class="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">Home</a>
                        <a href="main.html#meditations" class="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">Meditations</a>
                        <a href="games.html" class="text-gray-900 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">Games</a>
                        <a href="main.html#features" class="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">Features</a>
                        <a href="main.html#about" class="text-gray-600 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors">About</a>
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-xl">
                            Start Free Trial
                        </button>
                    </div>
                </div>
                <div class="md:hidden">
                    <button class="text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-16 min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
        <!-- Animated Background Elements -->
        <div class="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 floating"></div>
        <div class="absolute bottom-20 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 floating" style="animation-delay: 2s;"></div>
        <div class="absolute top-1/2 left-1/2 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating" style="animation-delay: 4s;"></div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center mb-16">
                <h1 class="text-6xl font-display font-bold text-gray-900 mb-6">
                    Mindful <span class="text-indigo-600">Games</span>
                </h1>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
                    Discover peaceful, mind-motivating games designed to enhance your mental wellness, reduce stress, and bring joy to your meditation journey.
                </p>
                <div class="breathing-circle w-20 h-20 bg-indigo-500 rounded-full mx-auto mb-8 flex items-center justify-center">
                    <i class="fas fa-gamepad text-white text-2xl"></i>
                </div>
            </div>

            <!-- Games Grid -->
            <div class="grid md:grid-cols-2 gap-8 mb-16 max-w-4xl mx-auto">
                <!-- Breathing Game -->
                <div class="game-card rounded-3xl p-8 cursor-pointer wave" onclick="openBreathingGame()">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full mx-auto mb-6 flex items-center justify-center breathing-circle">
                            <i class="fas fa-lungs text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Breathing Harmony</h3>
                        <p class="text-gray-600 mb-6">Follow the gentle rhythm and synchronize your breath with beautiful visual cues</p>
                        <button class="water-button ripple text-white px-6 py-3 font-medium">
                            Start Breathing
                        </button>
                    </div>
                </div>

                <!-- Zen Garden -->
                <div class="game-card rounded-3xl p-8 cursor-pointer wave" onclick="openZenGarden()">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-600 rounded-full mx-auto mb-6 flex items-center justify-center">
                            <svg width="40" height="40" viewBox="0 0 100 100" class="text-white">
                                <path class="rake-line" stroke="currentColor" stroke-width="3" fill="none" d="M20,50 Q50,20 80,50 Q50,80 20,50"/>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Zen Garden</h3>
                        <p class="text-gray-600 mb-6">Create beautiful patterns in the sand and find peace through digital mindfulness</p>
                        <button class="water-button ripple text-white px-6 py-3 font-medium">
                            Enter Garden
                        </button>
                    </div>
                </div>

                <!-- Memory Mindfulness -->
                <div class="game-card rounded-3xl p-8 cursor-pointer wave" onclick="openMemoryGame()">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-orange-400 to-red-500 rounded-full mx-auto mb-6 flex items-center justify-center">
                            <i class="fas fa-brain text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Mindful Memory</h3>
                        <p class="text-gray-600 mb-6">Gentle memory exercises that enhance focus while maintaining inner calm</p>
                        <button class="water-button ripple text-white px-6 py-3 font-medium">
                            Train Mind
                        </button>
                    </div>
                </div>

                <!-- Gratitude Garden -->
                <div class="game-card rounded-3xl p-8 cursor-pointer wave" onclick="openGratitudeGarden()">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full mx-auto mb-6 flex items-center justify-center">
                            <i class="fas fa-seedling text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Gratitude Garden</h3>
                        <p class="text-gray-600 mb-6">Plant seeds of gratitude and watch your virtual garden bloom with positivity</p>
                        <button class="water-button ripple text-white px-6 py-3 font-medium">
                            Plant Seeds
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Game Containers (Hidden by default) -->
    <div id="gameContainer" class="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 hidden">
        <div class="h-full flex flex-col">
            <!-- Game Header -->
            <div class="flex justify-between items-center p-6 bg-white/10 backdrop-blur-md">
                <h2 id="gameTitle" class="text-2xl font-bold text-white">Game Title</h2>
                <button id="closeGameBtn" class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200">
                    <i class="fas fa-times mr-2"></i>Close
                </button>
            </div>

            <!-- Game Content -->
            <div id="gameContent" class="flex-1 overflow-hidden">
                <!-- Game content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        let currentGame = null;

        // Game Functions
        function openBreathingGame() {
            loadGame('Breathing Harmony', createBreathingGame());
        }

        function openZenGarden() {
            loadGame('Zen Garden', createZenGarden());
        }

        function openMemoryGame() {
            loadGame('Mindful Memory', createMemoryGame());
        }

        function openGratitudeGarden() {
            loadGame('Gratitude Garden', createGratitudeGarden());
        }

        // Load game in container
        function loadGame(title, gameHTML) {
            document.getElementById('gameTitle').textContent = title;
            document.getElementById('gameContent').innerHTML = gameHTML;
            document.getElementById('gameContainer').classList.remove('hidden');
            currentGame = title;

            // Initialize game-specific scripts
            initializeCurrentGame();
        }

        // Close game
        function closeGame() {
            document.getElementById('gameContainer').classList.add('hidden');
            document.getElementById('gameContent').innerHTML = '';
            currentGame = null;
        }

        // Event listeners
        document.getElementById('closeGameBtn').addEventListener('click', closeGame);

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && currentGame) {
                closeGame();
            }
        });

        // Add watery animations to buttons
        document.addEventListener('DOMContentLoaded', function() {
            const gameCards = document.querySelectorAll('.game-card');

            gameCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });

        // Game Creation Functions
        function createBreathingGame() {
            return `
                <div class="h-full flex items-center justify-center relative" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <!-- Floating Particles -->
                    <div class="floating-particle" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
                    <div class="floating-particle" style="top: 30%; right: 15%; animation-delay: 1s;"></div>
                    <div class="floating-particle" style="bottom: 25%; left: 20%; animation-delay: 2s;"></div>
                    <div class="floating-particle" style="bottom: 35%; right: 25%; animation-delay: 3s;"></div>

                    <div class="text-center text-white">
                        <h2 class="text-3xl font-bold mb-4">Follow the circle and breathe deeply</h2>

                        <!-- Breathing Circle Container -->
                        <div class="relative flex items-center justify-center mb-8">
                            <div id="breathingCircle" class="breathing-circle">
                                <div class="inner-circle"></div>
                            </div>
                        </div>

                        <!-- Instructions -->
                        <div id="instructionText" class="instruction-text text-2xl font-medium mb-8">
                            Click to start your breathing journey
                        </div>

                        <!-- Controls -->
                        <div class="flex justify-center space-x-4 mb-6">
                            <button id="startBtn" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm">
                                <i class="fas fa-play mr-2"></i>Start
                            </button>
                            <button id="pauseBtn" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm" style="display: none;">
                                <i class="fas fa-pause mr-2"></i>Pause
                            </button>
                            <button id="resetBtn" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm">
                                <i class="fas fa-refresh mr-2"></i>Reset
                            </button>
                        </div>

                        <!-- Stats -->
                        <div class="flex justify-center space-x-8 text-sm opacity-80">
                            <div>
                                <div class="text-lg font-bold" id="breathCount">0</div>
                                <div>Breaths</div>
                            </div>
                            <div>
                                <div class="text-lg font-bold" id="sessionTime">0:00</div>
                                <div>Time</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function createZenGarden() {
            return `
                <div class="h-full flex flex-col items-center justify-center p-4" style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-gray-800 mb-2">Create beautiful patterns in the sand</h2>
                        <p class="text-gray-600">Use different tools to find your inner peace</p>
                    </div>

                    <!-- Canvas Container -->
                    <div class="relative mb-6">
                        <canvas id="zenCanvas" width="700" height="400" class="rounded-2xl shadow-2xl" style="background: linear-gradient(45deg, #f4f1de 0%, #e9c46a 100%);"></canvas>
                    </div>

                    <!-- Tools -->
                    <div class="flex justify-center space-x-4">
                        <button id="rakeBtn" class="tool-button active px-4 py-2 rounded-lg font-medium bg-white/90 backdrop-blur-10">
                            <i class="fas fa-grip-lines mr-2"></i>Rake
                        </button>
                        <button id="stoneBtn" class="tool-button px-4 py-2 rounded-lg font-medium bg-white/90 backdrop-blur-10">
                            <i class="fas fa-circle mr-2"></i>Stone
                        </button>
                        <button id="waveBtn" class="tool-button px-4 py-2 rounded-lg font-medium bg-white/90 backdrop-blur-10">
                            <i class="fas fa-water mr-2"></i>Wave
                        </button>
                        <button id="spiralBtn" class="tool-button px-4 py-2 rounded-lg font-medium bg-white/90 backdrop-blur-10">
                            <i class="fas fa-hurricane mr-2"></i>Spiral
                        </button>
                        <button id="clearBtn" class="tool-button px-4 py-2 rounded-lg font-medium bg-white/90 backdrop-blur-10">
                            <i class="fas fa-eraser mr-2"></i>Clear
                        </button>
                    </div>
                </div>
            `;
        }



        function createMemoryGame() {
            return `
                <div class="h-full p-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="max-w-4xl mx-auto h-full flex flex-col justify-center">
                        <!-- Stats -->
                        <div class="flex justify-center space-x-6 mb-8">
                            <div class="stats-card rounded-lg px-6 py-3 text-center" style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);">
                                <div class="text-2xl font-bold text-white" id="moves">0</div>
                                <div class="text-white/80 text-sm">Moves</div>
                            </div>
                            <div class="stats-card rounded-lg px-6 py-3 text-center" style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);">
                                <div class="text-2xl font-bold text-white" id="matches">0</div>
                                <div class="text-white/80 text-sm">Matches</div>
                            </div>
                            <div class="stats-card rounded-lg px-6 py-3 text-center" style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);">
                                <div class="text-2xl font-bold text-white" id="timer">0:00</div>
                                <div class="text-white/80 text-sm">Time</div>
                            </div>
                        </div>

                        <!-- Game Board -->
                        <div id="gameBoard" class="grid grid-cols-4 gap-4 max-w-2xl mx-auto mb-8">
                            <!-- Cards will be generated here -->
                        </div>

                        <!-- Controls -->
                        <div class="text-center">
                            <button id="newGameBtn" class="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm font-medium">
                                <i class="fas fa-refresh mr-2"></i>New Game
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }



        function createGratitudeGarden() {
            return `
                <div class="h-full p-4" style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00b894 100%);">
                    <div class="max-w-6xl mx-auto h-full flex flex-col justify-center">
                        <!-- Stats -->
                        <div class="flex justify-center space-x-6 mb-6">
                            <div class="garden-stats rounded-lg px-6 py-3 text-center" style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);">
                                <div class="text-2xl font-bold text-white" id="plantsCount">0</div>
                                <div class="text-white/80 text-sm">Plants</div>
                            </div>
                            <div class="garden-stats rounded-lg px-6 py-3 text-center" style="background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);">
                                <div class="text-2xl font-bold text-white" id="gratitudeCount">0</div>
                                <div class="text-white/80 text-sm">Gratitudes</div>
                            </div>
                        </div>

                        <!-- Seed Packets -->
                        <div class="flex justify-center space-x-4 mb-6">
                            <div class="seed-packet rounded-lg p-3 cursor-pointer bg-yellow-200 border-2 border-orange-400" data-plant="🌱" data-name="Seedling">
                                <div class="text-2xl text-center">🌱</div>
                                <div class="text-xs text-center mt-1">Seedling</div>
                            </div>
                            <div class="seed-packet rounded-lg p-3 cursor-pointer bg-yellow-200 border-2 border-orange-400" data-plant="🌸" data-name="Flower">
                                <div class="text-2xl text-center">🌸</div>
                                <div class="text-xs text-center mt-1">Flower</div>
                            </div>
                            <div class="seed-packet rounded-lg p-3 cursor-pointer bg-yellow-200 border-2 border-orange-400" data-plant="🌻" data-name="Sunflower">
                                <div class="text-2xl text-center">🌻</div>
                                <div class="text-xs text-center mt-1">Sunflower</div>
                            </div>
                            <div class="seed-packet rounded-lg p-3 cursor-pointer bg-yellow-200 border-2 border-orange-400" data-plant="🌺" data-name="Hibiscus">
                                <div class="text-2xl text-center">🌺</div>
                                <div class="text-xs text-center mt-1">Hibiscus</div>
                            </div>
                            <div class="seed-packet rounded-lg p-3 cursor-pointer bg-yellow-200 border-2 border-orange-400" data-plant="🌷" data-name="Tulip">
                                <div class="text-2xl text-center">🌷</div>
                                <div class="text-xs text-center mt-1">Tulip</div>
                            </div>
                        </div>

                        <!-- Garden Grid -->
                        <div class="grid grid-cols-6 gap-4 max-w-3xl mx-auto mb-6" id="gardenGrid">
                            <!-- Garden plots will be generated here -->
                        </div>

                        <!-- Instructions -->
                        <div class="text-center text-white/80 text-sm">
                            <p>Choose a seed, click an empty plot, and share what you're grateful for</p>
                        </div>
                    </div>

                    <!-- Gratitude Modal -->
                    <div id="gratitudeModal" class="fixed inset-0 bg-black/50 flex items-center justify-center hidden z-10">
                        <div class="bg-white/95 backdrop-blur-10 rounded-2xl p-8 max-w-md mx-4">
                            <h3 class="text-2xl font-bold text-gray-800 mb-4 text-center">Plant Your Gratitude</h3>
                            <p class="text-gray-600 mb-4 text-center">What are you grateful for today?</p>
                            <textarea id="gratitudeText" class="w-full p-3 border border-gray-300 rounded-lg resize-none" rows="3" placeholder="I am grateful for..."></textarea>
                            <div class="flex justify-center space-x-4 mt-6">
                                <button id="plantBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-seedling mr-2"></i>Plant
                                </button>
                                <button id="cancelBtn" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-lg transition-colors">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Initialize game-specific functionality
        function initializeCurrentGame() {
            setTimeout(() => {
                if (currentGame === 'Breathing Harmony') {
                    initBreathingGame();
                } else if (currentGame === 'Zen Garden') {
                    initZenGarden();
                } else if (currentGame === 'Mindful Memory') {
                    initMemoryGame();
                } else if (currentGame === 'Gratitude Garden') {
                    initGratitudeGarden();
                }
            }, 100);
        }

        // Game Initialization Functions
        function initBreathingGame() {
            let isBreathing = false;
            let breathCount = 0;
            let sessionStartTime = null;
            let breathingInterval = null;
            let timeInterval = null;

            const circle = document.getElementById('breathingCircle');
            const instructionText = document.getElementById('instructionText');
            const startBtn = document.getElementById('startBtn');
            const pauseBtn = document.getElementById('pauseBtn');
            const resetBtn = document.getElementById('resetBtn');
            const breathCountEl = document.getElementById('breathCount');
            const sessionTimeEl = document.getElementById('sessionTime');

            function startBreathing() {
                if (isBreathing) return;
                isBreathing = true;
                sessionStartTime = Date.now();
                startBtn.style.display = 'none';
                pauseBtn.style.display = 'inline-block';
                timeInterval = setInterval(updateTime, 1000);
                breathingCycle();
            }

            function pauseBreathing() {
                isBreathing = false;
                clearTimeout(breathingInterval);
                clearInterval(timeInterval);
                startBtn.style.display = 'inline-block';
                pauseBtn.style.display = 'none';
                instructionText.textContent = 'Paused - Click Start to continue';
                circle.className = 'breathing-circle';
            }

            function resetBreathing() {
                isBreathing = false;
                breathCount = 0;
                sessionStartTime = null;
                clearTimeout(breathingInterval);
                clearInterval(timeInterval);
                startBtn.style.display = 'inline-block';
                pauseBtn.style.display = 'none';
                circle.className = 'breathing-circle';
                instructionText.textContent = 'Click to start your breathing journey';
                breathCountEl.textContent = '0';
                sessionTimeEl.textContent = '0:00';
            }

            function breathingCycle() {
                if (!isBreathing) return;
                circle.className = 'breathing-circle inhale';
                instructionText.textContent = 'Breathe In...';

                breathingInterval = setTimeout(() => {
                    if (!isBreathing) return;
                    instructionText.textContent = 'Hold...';
                    setTimeout(() => {
                        if (!isBreathing) return;
                        circle.className = 'breathing-circle exhale';
                        instructionText.textContent = 'Breathe Out...';
                        setTimeout(() => {
                            if (!isBreathing) return;
                            instructionText.textContent = 'Hold...';
                            setTimeout(() => {
                                if (!isBreathing) return;
                                breathCount++;
                                breathCountEl.textContent = breathCount;
                                breathingCycle();
                            }, 1000);
                        }, 6000);
                    }, 1000);
                }, 4000);
            }

            function updateTime() {
                if (!sessionStartTime) return;
                const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                sessionTimeEl.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }

            startBtn.addEventListener('click', startBreathing);
            pauseBtn.addEventListener('click', pauseBreathing);
            resetBtn.addEventListener('click', resetBreathing);
        }

        // Game Initialization Functions


        function initZenGarden() {
            const canvas = document.getElementById('zenCanvas');
            const ctx = canvas.getContext('2d');
            let isDrawing = false;
            let currentTool = 'rake';
            let lastX = 0;
            let lastY = 0;

            // Audio setup for zen garden sounds
            const sandPourAudio = new Audio('pouring-sand-onto-sand-8-seconds-291368.mp3');
            const sandWalkAudio = new Audio('water-relaxing-sound-121599.mp3'); // Using water sound as alternative
            sandPourAudio.volume = 0.3;
            sandWalkAudio.volume = 0.3;

            function playDrawingSound() {
                // Play different sounds for different tools
                if (currentTool === 'rake' || currentTool === 'wave') {
                    // Use sand walk sound for continuous drawing
                    if (sandWalkAudio.paused) {
                        sandWalkAudio.currentTime = 0;
                        sandWalkAudio.play().catch(e => console.log('Audio play failed:', e));
                    }
                } else if (currentTool === 'stone' || currentTool === 'spiral') {
                    // Use sand pour sound for placement tools
                    sandPourAudio.currentTime = 0;
                    sandPourAudio.play().catch(e => console.log('Audio play failed:', e));
                }
            }

            function initCanvas() {
                ctx.fillStyle = '#f4f1de';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                for (let i = 0; i < 500; i++) {
                    ctx.fillStyle = `rgba(233, 196, 106, ${Math.random() * 0.1})`;
                    ctx.fillRect(Math.random() * canvas.width, Math.random() * canvas.height, 2, 2);
                }
            }

            function setTool(tool) {
                currentTool = tool;
                document.querySelectorAll('.tool-button').forEach(btn => btn.classList.remove('active'));
                document.getElementById(tool + 'Btn').classList.add('active');
            }

            function drawRake(x, y) {
                ctx.strokeStyle = '#d4a574';
                ctx.lineWidth = 3;
                ctx.lineCap = 'round';
                ctx.beginPath();
                ctx.moveTo(lastX, lastY);
                ctx.lineTo(x, y);
                ctx.stroke();

                const angle = Math.atan2(y - lastY, x - lastX);
                for (let i = -2; i <= 2; i++) {
                    const offsetX = Math.cos(angle + Math.PI/2) * i * 8;
                    const offsetY = Math.sin(angle + Math.PI/2) * i * 8;
                    ctx.beginPath();
                    ctx.moveTo(lastX + offsetX, lastY + offsetY);
                    ctx.lineTo(x + offsetX, y + offsetY);
                    ctx.stroke();
                }
            }

            function drawStone(x, y) {
                const radius = 20 + Math.random() * 20;
                const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
                gradient.addColorStop(0, '#8d7053');
                gradient.addColorStop(1, '#5d4e37');

                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, Math.PI * 2);
                ctx.fill();

                // Add highlight
                ctx.fillStyle = 'rgba(255,255,255,0.3)';
                ctx.beginPath();
                ctx.arc(x - radius/3, y - radius/3, radius/3, 0, Math.PI * 2);
                ctx.fill();
            }

            function drawWave(x, y) {
                ctx.strokeStyle = '#b8a082';
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';

                const amplitude = 20;
                const frequency = 0.02;

                ctx.beginPath();
                for (let i = 0; i < 100; i++) {
                    const waveX = lastX + (x - lastX) * (i / 100);
                    const waveY = lastY + (y - lastY) * (i / 100) + Math.sin(i * frequency) * amplitude;

                    if (i === 0) {
                        ctx.moveTo(waveX, waveY);
                    } else {
                        ctx.lineTo(waveX, waveY);
                    }
                }
                ctx.stroke();
            }

            function drawSpiral(x, y) {
                ctx.strokeStyle = '#c9b037';
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';

                const centerX = x;
                const centerY = y;
                const maxRadius = 50;

                ctx.beginPath();
                for (let angle = 0; angle < Math.PI * 6; angle += 0.1) {
                    const radius = (angle / (Math.PI * 6)) * maxRadius;
                    const spiralX = centerX + Math.cos(angle) * radius;
                    const spiralY = centerY + Math.sin(angle) * radius;

                    if (angle === 0) {
                        ctx.moveTo(spiralX, spiralY);
                    } else {
                        ctx.lineTo(spiralX, spiralY);
                    }
                }
                ctx.stroke();
            }

            canvas.addEventListener('mousedown', (e) => {
                isDrawing = true;
                const rect = canvas.getBoundingClientRect();
                lastX = e.clientX - rect.left;
                lastY = e.clientY - rect.top;

                // Play sound when starting to draw
                playDrawingSound();

                if (currentTool === 'stone') {
                    drawStone(lastX, lastY);
                } else if (currentTool === 'spiral') {
                    drawSpiral(lastX, lastY);
                }
            });

            canvas.addEventListener('mousemove', (e) => {
                if (!isDrawing) return;
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                if (currentTool === 'rake') {
                    drawRake(x, y);
                } else if (currentTool === 'wave') {
                    drawWave(x, y);
                }

                lastX = x;
                lastY = y;
            });

            canvas.addEventListener('mouseup', () => {
                isDrawing = false;
                // Stop continuous sounds when drawing ends
                sandWalkAudio.pause();
            });

            canvas.addEventListener('mouseleave', () => {
                isDrawing = false;
                // Stop continuous sounds when mouse leaves canvas
                sandWalkAudio.pause();
            });

            // Tool button events
            document.getElementById('rakeBtn').addEventListener('click', () => setTool('rake'));
            document.getElementById('stoneBtn').addEventListener('click', () => setTool('stone'));
            document.getElementById('waveBtn').addEventListener('click', () => setTool('wave'));
            document.getElementById('spiralBtn').addEventListener('click', () => setTool('spiral'));
            document.getElementById('clearBtn').addEventListener('click', initCanvas);

            initCanvas();
        }



        function initMemoryGame() {
            const symbols = ['🌸', '🍃', '🌙', '⭐', '🦋', '🌺', '🕊️', '🌿'];
            let cards = [];
            let flippedCards = [];
            let moves = 0;
            let matches = 0;
            let gameStartTime = null;
            let timerInterval = null;

            function initGame() {
                const gameBoard = document.getElementById('gameBoard');
                gameBoard.innerHTML = '';
                cards = [];
                flippedCards = [];
                moves = 0;
                matches = 0;
                gameStartTime = null;

                const cardSymbols = [...symbols, ...symbols];
                cardSymbols.sort(() => Math.random() - 0.5);

                cardSymbols.forEach((symbol, index) => {
                    const card = document.createElement('div');
                    card.className = 'memory-card w-24 h-24 perspective-1000 cursor-pointer';
                    card.dataset.symbol = symbol;
                    card.innerHTML = `
                        <div class="card-inner relative w-full h-full text-center transition-transform duration-600 transform-style-preserve-3d">
                            <div class="card-front absolute w-full h-full backface-hidden rounded-xl flex items-center justify-center text-2xl shadow-lg" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #667eea;">
                                <i class="fas fa-question"></i>
                            </div>
                            <div class="card-back absolute w-full h-full backface-hidden rounded-xl flex items-center justify-center text-2xl shadow-lg" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #8b5a3c; transform: rotateY(180deg);">
                                ${symbol}
                            </div>
                        </div>
                    `;
                    card.addEventListener('click', () => flipCard(card));
                    gameBoard.appendChild(card);
                    cards.push(card);
                });

                updateStats();
            }

            function flipCard(card) {
                if (card.classList.contains('flipped') || card.classList.contains('matched') || flippedCards.length >= 2) return;

                if (!gameStartTime) {
                    gameStartTime = Date.now();
                    timerInterval = setInterval(updateTimer, 1000);
                }

                card.classList.add('flipped');
                card.querySelector('.card-inner').style.transform = 'rotateY(180deg)';
                flippedCards.push(card);

                if (flippedCards.length === 2) {
                    moves++;
                    updateStats();
                    setTimeout(checkMatch, 600);
                }
            }

            function checkMatch() {
                const [card1, card2] = flippedCards;
                if (card1.dataset.symbol === card2.dataset.symbol) {
                    // Match found
                    setTimeout(() => {
                        card1.classList.add('matched');
                        card2.classList.add('matched');
                        matches++;
                        updateStats();
                        if (matches === symbols.length) {
                            setTimeout(() => alert('Congratulations! 🎉'), 500);
                        }
                        flippedCards = [];
                    }, 300);
                } else {
                    // No match - flip back after delay
                    setTimeout(() => {
                        card1.classList.remove('flipped');
                        card2.classList.remove('flipped');
                        card1.querySelector('.card-inner').style.transform = 'rotateY(0deg)';
                        card2.querySelector('.card-inner').style.transform = 'rotateY(0deg)';
                        flippedCards = [];
                    }, 1000);
                }
            }

            function updateStats() {
                document.getElementById('moves').textContent = moves;
                document.getElementById('matches').textContent = matches;
            }

            function updateTimer() {
                if (!gameStartTime) return;
                const elapsed = Math.floor((Date.now() - gameStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                document.getElementById('timer').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }

            document.getElementById('newGameBtn').addEventListener('click', () => {
                clearInterval(timerInterval);
                initGame();
            });

            initGame();
        }



        function initGratitudeGarden() {
            let selectedSeed = '🌱';
            let selectedSeedName = 'Seedling';
            let selectedPlot = null;
            let plantsCount = 0;
            let gratitudeCount = 0;
            let gratitudes = [];

            function initGarden() {
                const gardenGrid = document.getElementById('gardenGrid');
                for (let i = 0; i < 18; i++) {
                    const plot = document.createElement('div');
                    plot.className = 'garden-plot w-16 h-16 rounded-lg border-2 border-yellow-600 cursor-pointer transition-all duration-300 hover:transform hover:-translate-y-1';
                    plot.style.background = 'linear-gradient(135deg, #8b4513 0%, #a0522d 100%)';
                    plot.dataset.index = i;
                    plot.addEventListener('click', () => selectPlot(plot));
                    gardenGrid.appendChild(plot);
                }
            }

            function selectSeed(element) {
                document.querySelectorAll('.seed-packet').forEach(packet => {
                    packet.classList.remove('ring-4', 'ring-white');
                });
                element.classList.add('ring-4', 'ring-white');
                selectedSeed = element.dataset.plant;
                selectedSeedName = element.dataset.name;
            }

            function selectPlot(plot) {
                if (plot.classList.contains('planted')) {
                    // Show existing gratitude
                    const index = plot.dataset.index;
                    const gratitude = gratitudes[index];
                    if (gratitude) {
                        alert(`${gratitude.plant} planted!\n\n"${gratitude.text}"`);
                    }
                    return;
                }
                selectedPlot = plot;
                document.getElementById('gratitudeModal').classList.remove('hidden');
                document.getElementById('gratitudeText').focus();
            }

            function plantSeed() {
                const gratitudeText = document.getElementById('gratitudeText').value.trim();
                if (!gratitudeText) {
                    alert('Please share what you\'re grateful for!');
                    return;
                }

                selectedPlot.classList.add('planted');
                selectedPlot.style.background = 'linear-gradient(135deg, #2d5016 0%, #3e6b1f 100%)';

                const plant = document.createElement('div');
                plant.className = 'absolute bottom-2 left-1/2 transform -translate-x-1/2 text-2xl';
                plant.textContent = selectedSeed;
                plant.style.animation = 'grow 1s ease-out';
                selectedPlot.appendChild(plant);

                const index = selectedPlot.dataset.index;
                gratitudes[index] = { text: gratitudeText, plant: selectedSeed };

                plantsCount++;
                gratitudeCount++;
                document.getElementById('plantsCount').textContent = plantsCount;
                document.getElementById('gratitudeCount').textContent = gratitudeCount;

                closeModal();
            }

            function closeModal() {
                document.getElementById('gratitudeModal').classList.add('hidden');
                document.getElementById('gratitudeText').value = '';
                selectedPlot = null;
            }

            document.querySelectorAll('.seed-packet').forEach(packet => {
                packet.addEventListener('click', () => selectSeed(packet));
            });

            document.getElementById('plantBtn').addEventListener('click', plantSeed);
            document.getElementById('cancelBtn').addEventListener('click', closeModal);

            initGarden();
            selectSeed(document.querySelector('.seed-packet'));
        }
    </script>
</body>
</html>
