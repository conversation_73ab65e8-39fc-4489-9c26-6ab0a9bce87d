#!/usr/bin/env python3
"""
Simple HTTP server for serving the MindSpace meditation website
Optimized for Render deployment
"""

import http.server
import socketserver
import os
from urllib.parse import urlparse

PORT = int(os.environ.get('PORT', 8080))

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers for audio files
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # Cache static assets
        if self.path.endswith(('.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg')):
            self.send_header('Cache-Control', 'public, max-age=86400')  # 1 day
        elif self.path.endswith(('.mp3', '.wav', '.ogg')):
            self.send_header('Cache-Control', 'public, max-age=3600')   # 1 hour
            
        super().end_headers()

    def do_GET(self):
        # Serve index.html for root path
        if self.path == '/':
            self.path = '/index.html'
        return super().do_GET()

if __name__ == "__main__":
    with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
        print(f"🧘‍♀️ MindSpace server running on port {PORT}")
        print(f"🌐 Visit: http://localhost:{PORT}")
        httpd.serve_forever()
