<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sound Waves - MindSpace</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌊</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            overflow: hidden;
        }
        
        #waveCanvas {
            cursor: crosshair;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .control-button {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }
        
        .control-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .control-button.active {
            background: rgba(255,255,255,0.4);
            transform: translateY(-2px);
            box-shadow: 0 0 20px rgba(255,255,255,0.3);
        }
        
        .frequency-slider {
            -webkit-appearance: none;
            background: linear-gradient(to right, #4facfe 0%, #00f2fe 100%);
            outline: none;
            border-radius: 15px;
            height: 8px;
        }
        
        .frequency-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: white;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }
        
        .floating-note {
            position: absolute;
            color: rgba(255,255,255,0.7);
            font-size: 24px;
            pointer-events: none;
            animation: floatUp 3s ease-out forwards;
        }
        
        @keyframes floatUp {
            0% { transform: translateY(0) scale(1); opacity: 1; }
            100% { transform: translateY(-100px) scale(1.5); opacity: 0; }
        }
        
        .ripple-effect {
            position: absolute;
            border-radius: 50%;
            border: 2px solid rgba(255,255,255,0.5);
            animation: ripple 2s ease-out forwards;
            pointer-events: none;
        }
        
        @keyframes ripple {
            0% { width: 0; height: 0; opacity: 1; }
            100% { width: 200px; height: 200px; opacity: 0; }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <!-- Back Button -->
    <button onclick="window.close()" class="absolute top-6 left-6 control-button text-white px-4 py-2 rounded-lg">
        <i class="fas fa-arrow-left mr-2"></i>Back to Games
    </button>

    <!-- Instructions -->
    <div class="absolute top-6 right-6 text-white text-right">
        <div class="text-lg font-medium mb-1">Sound Waves</div>
        <div class="text-sm opacity-80">Click to create ripples of sound</div>
        <div class="text-xs opacity-60 mt-1">Adjust frequency • Hold for sustained notes</div>
    </div>

    <div class="max-w-6xl w-full">
        <!-- Canvas Container -->
        <div class="relative mb-6">
            <canvas id="waveCanvas" width="900" height="600" class="mx-auto block bg-gradient-to-br from-blue-900 to-indigo-900"></canvas>
        </div>

        <!-- Controls -->
        <div class="flex justify-center items-center space-x-6">
            <!-- Frequency Control -->
            <div class="text-white text-center">
                <div class="text-sm mb-2">Frequency</div>
                <input type="range" id="frequencySlider" min="100" max="1000" value="440" class="frequency-slider w-32">
                <div class="text-xs mt-1" id="frequencyDisplay">440 Hz</div>
            </div>
            
            <!-- Wave Type -->
            <div class="flex space-x-2">
                <button id="sineBtn" class="control-button active text-white px-4 py-2 rounded-lg text-sm">Sine</button>
                <button id="squareBtn" class="control-button text-white px-4 py-2 rounded-lg text-sm">Square</button>
                <button id="sawBtn" class="control-button text-white px-4 py-2 rounded-lg text-sm">Sawtooth</button>
            </div>
            
            <!-- Volume -->
            <div class="text-white text-center">
                <div class="text-sm mb-2">Volume</div>
                <input type="range" id="volumeSlider" min="0" max="100" value="30" class="frequency-slider w-24">
            </div>
            
            <!-- Clear -->
            <button id="clearBtn" class="control-button text-white px-6 py-2 rounded-lg">
                <i class="fas fa-eraser mr-2"></i>Clear
            </button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('waveCanvas');
        const ctx = canvas.getContext('2d');
        let audioContext = null;
        let oscillator = null;
        let gainNode = null;
        let isPlaying = false;
        let currentWaveType = 'sine';
        let waves = [];
        let animationId = null;

        // Wave class for visual effects
        class Wave {
            constructor(x, y, frequency, waveType) {
                this.x = x;
                this.y = y;
                this.frequency = frequency;
                this.waveType = waveType;
                this.radius = 0;
                this.maxRadius = Math.random() * 200 + 100;
                this.life = 1.0;
                this.decay = 0.005;
                this.color = this.getColorFromFrequency(frequency);
            }
            
            getColorFromFrequency(freq) {
                // Map frequency to color spectrum
                const hue = ((freq - 100) / 900) * 360;
                return `hsl(${hue}, 70%, 60%)`;
            }
            
            update() {
                this.radius += 2;
                this.life -= this.decay;
            }
            
            draw() {
                const alpha = this.life;
                ctx.strokeStyle = this.color.replace('60%', `60%, ${alpha}`);
                ctx.lineWidth = 2;
                ctx.setLineDash([]);
                
                // Draw main wave
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.stroke();
                
                // Draw wave pattern based on type
                if (this.waveType === 'sine') {
                    this.drawSinePattern();
                } else if (this.waveType === 'square') {
                    this.drawSquarePattern();
                } else if (this.waveType === 'sawtooth') {
                    this.drawSawtoothPattern();
                }
            }
            
            drawSinePattern() {
                ctx.strokeStyle = this.color.replace('60%', `60%, ${this.life * 0.5}`);
                ctx.lineWidth = 1;
                
                for (let angle = 0; angle < Math.PI * 2; angle += 0.1) {
                    const waveRadius = this.radius + Math.sin(angle * this.frequency / 100) * 10;
                    const x = this.x + Math.cos(angle) * waveRadius;
                    const y = this.y + Math.sin(angle) * waveRadius;
                    
                    if (angle === 0) {
                        ctx.beginPath();
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
            }
            
            drawSquarePattern() {
                ctx.strokeStyle = this.color.replace('60%', `60%, ${this.life * 0.5}`);
                ctx.lineWidth = 1;
                ctx.setLineDash([5, 5]);
                
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius * 0.8, 0, Math.PI * 2);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius * 1.2, 0, Math.PI * 2);
                ctx.stroke();
            }
            
            drawSawtoothPattern() {
                ctx.strokeStyle = this.color.replace('60%', `60%, ${this.life * 0.5}`);
                ctx.lineWidth = 1;
                
                const spikes = 12;
                ctx.beginPath();
                for (let i = 0; i <= spikes; i++) {
                    const angle = (i / spikes) * Math.PI * 2;
                    const radius = i % 2 === 0 ? this.radius * 0.8 : this.radius * 1.2;
                    const x = this.x + Math.cos(angle) * radius;
                    const y = this.y + Math.sin(angle) * radius;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.closePath();
                ctx.stroke();
            }
            
            isDead() {
                return this.life <= 0 || this.radius > this.maxRadius;
            }
        }

        // Initialize audio context
        function initAudio() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                gainNode = audioContext.createGain();
                gainNode.connect(audioContext.destination);
                gainNode.gain.value = 0.3;
            }
        }

        // Play sound
        function playSound(frequency) {
            initAudio();
            
            if (oscillator) {
                oscillator.stop();
            }
            
            oscillator = audioContext.createOscillator();
            oscillator.type = currentWaveType;
            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.connect(gainNode);
            oscillator.start();
            
            isPlaying = true;
        }

        // Stop sound
        function stopSound() {
            if (oscillator) {
                oscillator.stop();
                oscillator = null;
            }
            isPlaying = false;
        }

        // Create wave visual
        function createWave(x, y) {
            const frequency = parseInt(document.getElementById('frequencySlider').value);
            waves.push(new Wave(x, y, frequency, currentWaveType));
            
            // Create ripple effect
            const ripple = document.createElement('div');
            ripple.className = 'ripple-effect';
            ripple.style.left = (x - 100) + 'px';
            ripple.style.top = (y - 100) + 'px';
            document.body.appendChild(ripple);
            
            setTimeout(() => {
                document.body.removeChild(ripple);
            }, 2000);
            
            // Create floating note
            const note = document.createElement('div');
            note.className = 'floating-note';
            note.textContent = '♪';
            note.style.left = x + 'px';
            note.style.top = y + 'px';
            document.body.appendChild(note);
            
            setTimeout(() => {
                document.body.removeChild(note);
            }, 3000);
        }

        // Animation loop
        function animate() {
            // Clear with fade effect
            ctx.fillStyle = 'rgba(30, 60, 114, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw waves
            for (let i = waves.length - 1; i >= 0; i--) {
                const wave = waves[i];
                wave.update();
                wave.draw();
                
                if (wave.isDead()) {
                    waves.splice(i, 1);
                }
            }
            
            animationId = requestAnimationFrame(animate);
        }

        // Event listeners
        canvas.addEventListener('mousedown', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            createWave(x, y);
            
            const frequency = parseInt(document.getElementById('frequencySlider').value);
            playSound(frequency);
        });

        canvas.addEventListener('mouseup', stopSound);
        canvas.addEventListener('mouseleave', stopSound);

        // Frequency slider
        document.getElementById('frequencySlider').addEventListener('input', (e) => {
            document.getElementById('frequencyDisplay').textContent = e.target.value + ' Hz';
            
            if (isPlaying && oscillator) {
                oscillator.frequency.setValueAtTime(e.target.value, audioContext.currentTime);
            }
        });

        // Volume slider
        document.getElementById('volumeSlider').addEventListener('input', (e) => {
            if (gainNode) {
                gainNode.gain.value = e.target.value / 100;
            }
        });

        // Wave type buttons
        document.getElementById('sineBtn').addEventListener('click', () => setWaveType('sine'));
        document.getElementById('squareBtn').addEventListener('click', () => setWaveType('square'));
        document.getElementById('sawBtn').addEventListener('click', () => setWaveType('sawtooth'));

        function setWaveType(type) {
            currentWaveType = type;
            document.querySelectorAll('.control-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(type === 'sawtooth' ? 'sawBtn' : type + 'Btn').classList.add('active');
        }

        // Clear button
        document.getElementById('clearBtn').addEventListener('click', () => {
            waves = [];
            ctx.fillStyle = 'rgba(30, 60, 114, 1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        });

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                waves = [];
            }
        });

        // Initialize
        ctx.fillStyle = 'rgba(30, 60, 114, 1)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        animate();

        // Welcome effect
        setTimeout(() => {
            createWave(canvas.width / 2, canvas.height / 2);
        }, 500);
    </script>
</body>
</html>
